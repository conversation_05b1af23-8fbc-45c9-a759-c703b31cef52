package com.aiwardenmod.entity.ai;

import com.aiwardenmod.entity.custom.AIWardenEntity;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.ai.goal.Goal;

import java.util.EnumSet;

public class AggressiveAttackGoal extends Goal {
    private final AIWardenEntity warden;
    private LivingEntity target;
    private int attackCooldown = 0;
    private static final int ATTACK_INTERVAL = 20; // 1 second between attacks

    public AggressiveAttackGoal(AIWardenEntity warden) {
        this.warden = warden;
        this.setFlags(EnumSet.of(Goal.Flag.MOVE, Goal.Flag.LOOK));
    }

    @Override
    public boolean canUse() {
        this.target = this.warden.getTarget();
        if (this.target == null || !this.target.isAlive()) {
            return false;
        }

        // Only activate when in attack mode (reached same level as target)
        if (!this.warden.isInAttackMode()) {
            return false;
        }

        double distance = this.warden.distanceTo(this.target);
        return distance < 12.0; // Activate within 12 blocks
    }

    @Override
    public boolean canContinueToUse() {
        if (this.target == null || !this.target.isAlive()) {
            return false;
        }

        // Continue until target is dead or too far away
        double distance = this.warden.distanceTo(this.target);
        return distance < 20.0 && this.warden.isInAttackMode();
    }

    @Override
    public void start() {
        this.attackCooldown = 0;
        System.out.println("AI Warden: Aggressive attack mode started!");
    }

    @Override
    public void stop() {
        this.target = null;
        this.warden.setAttackMode(false);
        System.out.println("AI Warden: Aggressive attack mode ended");
    }

    @Override
    public void tick() {
        if (this.target == null) {
            return;
        }

        if (this.attackCooldown > 0) {
            this.attackCooldown--;
        }

        double distance = this.warden.distanceTo(this.target);

        // Always look at target
        this.warden.getLookControl().setLookAt(this.target, 30.0F, 30.0F);

        if (distance > 6.0) {
            // Too far - jump toward target aggressively
            jumpTowardTarget();
        } else if (distance <= 3.0 && this.attackCooldown <= 0) {
            // Close enough - perform melee attack
            performMeleeAttack();
        } else {
            // Medium distance - move toward target quickly
            this.warden.getNavigation().moveTo(this.target, 1.5D);
        }
    }

    private void jumpTowardTarget() {
        if (this.target == null || !this.warden.onGround()) {
            return;
        }

        double deltaX = this.target.getX() - this.warden.getX();
        double deltaZ = this.target.getZ() - this.warden.getZ();
        double distance = Math.sqrt(deltaX * deltaX + deltaZ * deltaZ);

        if (distance > 0 && distance < 10.0) {
            // Powerful jump toward target
            double jumpStrength = 0.7;
            double horizontalSpeed = 0.5;

            this.warden.setDeltaMovement(
                (deltaX / distance) * horizontalSpeed,
                jumpStrength,
                (deltaZ / distance) * horizontalSpeed
            );

            // Play aggressive sound
            this.warden.playSound(SoundEvents.WARDEN_ROAR, 1.5F, 0.8F);
            System.out.println("AI Warden: Jumping aggressively toward target!");
        }
    }

    private void performMeleeAttack() {
        if (this.target == null) {
            return;
        }

        // Perform the attack with reduced damage (handled by doHurtTarget override)
        boolean attackSuccess = this.warden.doHurtTarget(this.target);

        if (attackSuccess) {
            // Add reduced knockback for aggressive attack
            double deltaX = this.target.getX() - this.warden.getX();
            double deltaZ = this.target.getZ() - this.warden.getZ();
            double distance = Math.sqrt(deltaX * deltaX + deltaZ * deltaZ);

            if (distance > 0) {
                // Reduced knockback (was 1.2, now 0.6)
                this.target.setDeltaMovement(
                    (deltaX / distance) * 0.6,
                    0.2, // Reduced upward velocity
                    (deltaZ / distance) * 0.6
                );
            }

            // Play attack sound
            this.warden.playSound(SoundEvents.WARDEN_ATTACK_IMPACT, 2.0F, 0.8F);
            System.out.println("AI Warden: Aggressive melee attack successful (REDUCED DAMAGE)!");
        }

        this.attackCooldown = ATTACK_INTERVAL;
    }
}
