package com.aiwardenmod.entity;

import com.aiwardenmod.AIWardenMod;
import com.aiwardenmod.entity.custom.AIWardenEntity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.MobCategory;
import net.minecraftforge.eventbus.api.IEventBus;
import net.minecraftforge.registries.DeferredRegister;
import net.minecraftforge.registries.ForgeRegistries;
import net.minecraftforge.registries.RegistryObject;

public class ModEntities {
    public static final DeferredRegister<EntityType<?>> ENTITY_TYPES =
            DeferredRegister.create(ForgeRegistries.ENTITY_TYPES, AIWardenMod.MODID);

    public static final RegistryObject<EntityType<AIWardenEntity>> AI_WARDEN =
            ENTITY_TYPES.register("ai_warden", () -> EntityType.Builder.of(AIWardenEntity::new, MobCategory.MONSTER)
                    .sized(0.9f, 2.9f)
                    .build("ai_warden"));

    public static void register(IEventBus eventBus) {
        ENTITY_TYPES.register(eventBus);
    }
}
