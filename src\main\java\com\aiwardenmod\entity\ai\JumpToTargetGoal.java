package com.aiwardenmod.entity.ai;

import com.aiwardenmod.entity.custom.AIWardenEntity;
import net.minecraft.core.BlockPos;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.ai.goal.Goal;
import net.minecraft.world.level.Level;
import net.minecraft.world.phys.Vec3;

import java.util.EnumSet;

public class JumpToTargetGoal extends Goal {
    private final AIWardenEntity warden;
    private final double speedModifier;
    private LivingEntity target;
    private int jumpAttemptCooldown = 0;
    private static final int JUMP_ATTEMPT_INTERVAL = 60; // 3 seconds

    public JumpToTargetGoal(AIWardenEntity warden, double speedModifier) {
        this.warden = warden;
        this.speedModifier = speedModifier;
        this.setFlags(EnumSet.of(Goal.Flag.MOVE, Goal.Flag.JUMP));
    }

    @Override
    public boolean canUse() {
        this.target = this.warden.getTarget();
        if (this.target == null || !this.target.isAlive()) {
            return false;
        }

        // Only use this goal if the target is higher and within jumping range
        double heightDifference = this.target.getY() - this.warden.getY();
        double horizontalDistance = this.warden.distanceTo(this.target);
        
        return this.warden.canJump() && 
               heightDifference > 1.0 && heightDifference < 4.0 &&
               horizontalDistance < 8.0 && horizontalDistance > 2.0 &&
               this.warden.onGround();
    }

    @Override
    public boolean canContinueToUse() {
        if (this.target == null || !this.target.isAlive()) {
            return false;
        }

        double heightDifference = this.target.getY() - this.warden.getY();
        double horizontalDistance = this.warden.distanceTo(this.target);
        
        return heightDifference > 0.5 && horizontalDistance < 10.0 && this.jumpAttemptCooldown > 0;
    }

    @Override
    public void start() {
        this.jumpAttemptCooldown = 0;
    }

    @Override
    public void stop() {
        this.target = null;
        this.jumpAttemptCooldown = 0;
    }

    @Override
    public void tick() {
        if (this.target == null) {
            return;
        }

        if (this.jumpAttemptCooldown > 0) {
            this.jumpAttemptCooldown--;
            return;
        }

        // Check if we should attempt a jump
        if (shouldAttemptJump()) {
            if (this.warden.tryJumpToTarget(this.target)) {
                this.jumpAttemptCooldown = JUMP_ATTEMPT_INTERVAL;
            } else {
                this.jumpAttemptCooldown = 20; // Shorter cooldown if jump failed
            }
        }
    }

    private boolean shouldAttemptJump() {
        if (!this.warden.onGround() || this.target == null) {
            return false;
        }

        Level level = this.warden.level();
        BlockPos wardenPos = this.warden.blockPosition();
        BlockPos targetPos = this.target.blockPosition();
        
        // Check if there's a clear path to jump
        Vec3 wardenVec = this.warden.position();
        Vec3 targetVec = this.target.position();
        
        double deltaX = targetVec.x - wardenVec.x;
        double deltaY = targetVec.y - wardenVec.y;
        double deltaZ = targetVec.z - wardenVec.z;
        double horizontalDistance = Math.sqrt(deltaX * deltaX + deltaZ * deltaZ);
        
        // Only jump if target is higher and within reasonable range
        if (deltaY < 1.0 || deltaY > 4.0 || horizontalDistance > 7.0 || horizontalDistance < 2.5) {
            return false;
        }

        // Check if there are obstacles in the way
        if (hasObstaclesInPath(wardenPos, targetPos)) {
            return false;
        }

        // Check if the warden's current path is blocked or inefficient
        if (isPathBlocked() || isPathInefficient()) {
            return true;
        }

        return false;
    }

    private boolean hasObstaclesInPath(BlockPos start, BlockPos end) {
        Level level = this.warden.level();
        
        // Simple raycast to check for obstacles
        Vec3 startVec = Vec3.atCenterOf(start);
        Vec3 endVec = Vec3.atCenterOf(end);
        Vec3 direction = endVec.subtract(startVec).normalize();
        
        double distance = startVec.distanceTo(endVec);
        int steps = (int) Math.ceil(distance);
        
        for (int i = 1; i < steps; i++) {
            Vec3 checkPos = startVec.add(direction.scale(i));
            BlockPos blockPos = BlockPos.containing(checkPos);
            
            // Check if there's a solid block at head height
            if (!level.isEmptyBlock(blockPos.above()) || !level.isEmptyBlock(blockPos.above(2))) {
                return true;
            }
        }
        
        return false;
    }

    private boolean isPathBlocked() {
        // Check if the warden's navigation is stuck
        return this.warden.getNavigation().isStuck() || 
               this.warden.getNavigation().isDone() && 
               this.warden.distanceTo(this.target) > 3.0;
    }

    private boolean isPathInefficient() {
        if (this.target == null) {
            return false;
        }

        // Check if the warden has been trying to reach the target for a while
        // without making significant progress
        double currentDistance = this.warden.distanceTo(this.target);
        
        // If we're close enough horizontally but target is still higher, jumping might help
        double horizontalDistance = Math.sqrt(
            Math.pow(this.target.getX() - this.warden.getX(), 2) + 
            Math.pow(this.target.getZ() - this.warden.getZ(), 2)
        );
        
        double heightDifference = this.target.getY() - this.warden.getY();
        
        return horizontalDistance < 4.0 && heightDifference > 1.5;
    }
}
