package com.aiwardenmod.entity.custom;

import com.aiwardenmod.entity.ai.BuildBlocksToTargetGoal;
import com.aiwardenmod.entity.ai.EnhancedChaseGoal;
import com.aiwardenmod.entity.ai.FlyToTargetGoal;
import com.aiwardenmod.entity.ai.JumpToTargetGoal;
import net.minecraft.core.BlockPos;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.ai.attributes.AttributeSupplier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.goal.*;
import net.minecraft.world.entity.ai.goal.target.HurtByTargetGoal;
import net.minecraft.world.entity.ai.goal.target.NearestAttackableTargetGoal;
import net.minecraft.world.entity.monster.Monster;
import net.minecraft.world.entity.monster.warden.Warden;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.block.state.BlockState;
import org.jetbrains.annotations.Nullable;

public class AIWardenEntity extends Warden {
    private int blockBuildCooldown = 0;
    private int jumpCooldown = 0;
    private int flyCooldown = 0;
    private boolean isFlying = false;
    private int flyingTicks = 0;
    private LivingEntity flyTarget = null;

    // Fear Lock tracking
    private final java.util.Map<Player, Integer> fearLockTimers = new java.util.HashMap<>();
    private final java.util.Set<Player> fearLockedPlayers = new java.util.HashSet<>();

    private static final int MAX_BUILD_COOLDOWN = 20; // 1 second - very frequent building
    private static final int MAX_JUMP_COOLDOWN = 40; // 2 seconds at 20 ticks per second
    private static final int MAX_FLY_COOLDOWN = 400; // 20 seconds - much less frequent flying
    private static final int FLY_DURATION = 60; // 3 seconds of flying
    private static final int FEAR_LOCK_TIME = 60; // 3 seconds of looking
    private static final int FEAR_LOCK_COOLDOWN = 200; // 10 seconds cooldown per player

    public AIWardenEntity(EntityType<? extends Warden> entityType, Level level) {
        super(entityType, level);
        this.xpReward = 50; // Same as regular Warden
    }

    public static AttributeSupplier.Builder createAttributes() {
        return Warden.createAttributes()
                .add(Attributes.MAX_HEALTH, 500.0D) // Same as Warden
                .add(Attributes.MOVEMENT_SPEED, 0.3D) // Slightly faster than Warden
                .add(Attributes.KNOCKBACK_RESISTANCE, 1.0D) // Immune to knockback
                .add(Attributes.ATTACK_KNOCKBACK, 1.5D)
                .add(Attributes.ATTACK_DAMAGE, 15.0D) // Half damage of regular Warden
                .add(Attributes.FOLLOW_RANGE, 24.0D); // Increased follow range
    }

    @Override
    protected void registerGoals() {
        this.goalSelector.addGoal(0, new FloatGoal(this));
        this.goalSelector.addGoal(1, new BuildBlocksToTargetGoal(this, 1.0D)); // Highest priority for building
        this.goalSelector.addGoal(2, new JumpToTargetGoal(this, 1.0D));
        this.goalSelector.addGoal(3, new FlyToTargetGoal(this)); // Lower priority for flying
        this.goalSelector.addGoal(4, new EnhancedChaseGoal(this, 1.2D, false));
        this.goalSelector.addGoal(5, new MeleeAttackGoal(this, 1.0D, false));
        this.goalSelector.addGoal(6, new WaterAvoidingRandomStrollGoal(this, 0.8D));
        this.goalSelector.addGoal(7, new LookAtPlayerGoal(this, Player.class, 8.0F));
        this.goalSelector.addGoal(8, new RandomLookAroundGoal(this));

        this.targetSelector.addGoal(1, new HurtByTargetGoal(this));
        this.targetSelector.addGoal(2, new NearestAttackableTargetGoal<>(this, Player.class, true));
    }

    @Override
    public void tick() {
        super.tick();

        // Decrease cooldowns
        if (blockBuildCooldown > 0) {
            blockBuildCooldown--;
        }
        if (jumpCooldown > 0) {
            jumpCooldown--;
        }
        if (flyCooldown > 0) {
            flyCooldown--;
        }

        // Handle flying behavior
        if (isFlying) {
            handleFlying();
        }

        // Handle Fear Lock
        handleFearLock();
    }

    public boolean canBuildBlocks() {
        return blockBuildCooldown <= 0;
    }

    public boolean canJump() {
        return jumpCooldown <= 0;
    }

    public boolean canFly() {
        return flyCooldown <= 0 && !isFlying;
    }

    public void setBlockBuildCooldown() {
        this.blockBuildCooldown = MAX_BUILD_COOLDOWN;
    }

    public void setJumpCooldown() {
        this.jumpCooldown = MAX_JUMP_COOLDOWN;
    }

    public void setFlyCooldown() {
        this.flyCooldown = MAX_FLY_COOLDOWN;
    }

    public boolean tryBuildBlock(BlockPos pos) {
        if (!canBuildBlocks() || !level().isEmptyBlock(pos)) {
            System.out.println("AI Warden: Cannot build - cooldown: " + !canBuildBlocks() + ", empty: " + !level().isEmptyBlock(pos));
            return false;
        }

        // Check if the position is reasonable for building
        if (pos.getY() > this.getY() + 5 || pos.getY() < this.getY() - 2) {
            System.out.println("AI Warden: Position not reasonable for building: " + pos);
            return false;
        }

        // Place a dirt block
        BlockState blockState = Blocks.DIRT.defaultBlockState();
        level().setBlock(pos, blockState, 3);
        setBlockBuildCooldown();

        // Play sound effect
        level().playSound(null, pos, SoundEvents.GRAVEL_PLACE, this.getSoundSource(), 1.0F, 1.0F);

        System.out.println("AI Warden: Successfully built block at " + pos);
        return true;
    }

    public boolean tryJumpToTarget(LivingEntity target) {
        if (!canJump() || target == null) {
            return false;
        }

        double deltaX = target.getX() - this.getX();
        double deltaZ = target.getZ() - this.getZ();
        double deltaY = target.getY() - this.getY();

        // Only jump if target is higher and within reasonable range
        if (deltaY > 0.5 && deltaY < 4.0) {
            double distance = Math.sqrt(deltaX * deltaX + deltaZ * deltaZ);
            if (distance < 8.0 && distance > 2.0) {
                // Calculate jump velocity
                double jumpStrength = Math.min(0.8, deltaY * 0.3 + 0.4);
                this.setDeltaMovement(deltaX * 0.1, jumpStrength, deltaZ * 0.1);
                setJumpCooldown();

                // Play jump sound
                this.playSound(SoundEvents.RAVAGER_STEP, 1.0F, 0.8F);
                return true;
            }
        }

        return false;
    }

    public boolean tryFlyToTarget(LivingEntity target) {
        if (!canFly() || target == null) {
            return false;
        }

        double heightDifference = target.getY() - this.getY();
        double horizontalDistance = Math.sqrt(
            Math.pow(target.getX() - this.getX(), 2) +
            Math.pow(target.getZ() - this.getZ(), 2)
        );

        // Only fly if target is significantly higher and within range
        if (heightDifference > 4.0 && horizontalDistance < 20.0) {
            startFlying(target);
            return true;
        }

        return false;
    }

    private void startFlying(LivingEntity target) {
        this.isFlying = true;
        this.flyingTicks = 0;
        this.flyTarget = target;
        this.setNoGravity(true);

        // Initial upward velocity
        this.setDeltaMovement(this.getDeltaMovement().add(0, 0.8, 0));

        // Play flying sound
        this.playSound(SoundEvents.ENDER_DRAGON_FLAP, 1.0F, 0.8F);

        setFlyCooldown();
    }

    private void handleFlying() {
        flyingTicks++;

        if (flyTarget == null || !flyTarget.isAlive() || flyingTicks > FLY_DURATION) {
            stopFlying();
            return;
        }

        // Calculate direction to target
        double deltaX = flyTarget.getX() - this.getX();
        double deltaY = flyTarget.getY() - this.getY();
        double deltaZ = flyTarget.getZ() - this.getZ();

        double distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY + deltaZ * deltaZ);

        if (distance < 2.0) {
            // Close enough to smash down
            performSmashAttack();
            return;
        }

        // Move towards target
        double speed = 0.3;
        this.setDeltaMovement(
            deltaX / distance * speed,
            Math.max(deltaY / distance * speed, -0.5), // Don't fall too fast
            deltaZ / distance * speed
        );

        // Play flying sounds occasionally
        if (flyingTicks % 20 == 0) {
            this.playSound(SoundEvents.PHANTOM_FLAP, 0.5F, 1.2F);
        }
    }

    private void performSmashAttack() {
        // Create a powerful downward attack
        this.setDeltaMovement(0, -1.5, 0); // Fast downward movement

        // Deal damage to nearby entities when landing
        if (this.onGround() || this.getY() <= flyTarget.getY() + 1) {
            // Create explosion-like effect
            this.level().explode(this, this.getX(), this.getY(), this.getZ(), 2.0F, Level.ExplosionInteraction.NONE);

            // Damage nearby entities
            for (LivingEntity entity : this.level().getEntitiesOfClass(LivingEntity.class,
                    this.getBoundingBox().inflate(3.0))) {
                if (entity != this && entity instanceof Player) {
                    entity.hurt(this.damageSources().mobAttack(this), 20.0F); // Smash damage
                    // Knockback effect
                    double deltaX = entity.getX() - this.getX();
                    double deltaZ = entity.getZ() - this.getZ();
                    double distance = Math.sqrt(deltaX * deltaX + deltaZ * deltaZ);
                    if (distance > 0) {
                        entity.setDeltaMovement(
                            deltaX / distance * 1.5,
                            0.5,
                            deltaZ / distance * 1.5
                        );
                    }
                }
            }

            // Play impact sound
            this.playSound(SoundEvents.GENERIC_EXPLODE, 1.0F, 0.8F);

            stopFlying();
        }
    }

    private void stopFlying() {
        this.isFlying = false;
        this.flyingTicks = 0;
        this.flyTarget = null;
        this.setNoGravity(false);
    }

    private void handleFearLock() {
        if (this.level().isClientSide) {
            return; // Only run on server
        }

        // Get all nearby players
        for (Player player : this.level().getEntitiesOfClass(Player.class, this.getBoundingBox().inflate(16.0))) {
            if (player.isSpectator() || player.isCreative()) {
                continue;
            }

            // Check if player is looking at the warden
            if (isPlayerLookingAtWarden(player)) {
                // Increment fear timer
                fearLockTimers.put(player, fearLockTimers.getOrDefault(player, 0) + 1);

                // Check if fear lock should activate
                if (fearLockTimers.get(player) >= FEAR_LOCK_TIME && !fearLockedPlayers.contains(player)) {
                    activateFearLock(player);
                }
            } else {
                // Reset timer if not looking
                fearLockTimers.put(player, 0);
            }
        }

        // Clean up timers for players who are too far away
        fearLockTimers.entrySet().removeIf(entry ->
            entry.getKey().distanceTo(this) > 20.0 || !entry.getKey().isAlive());
        fearLockedPlayers.removeIf(player ->
            player.distanceTo(this) > 30.0 || !player.isAlive());
    }

    private boolean isPlayerLookingAtWarden(Player player) {
        // Calculate if player is looking directly at the warden
        net.minecraft.world.phys.Vec3 playerLook = player.getViewVector(1.0F);
        net.minecraft.world.phys.Vec3 toWarden = this.position().subtract(player.position()).normalize();

        // Check if the angle between look direction and direction to warden is small
        double dotProduct = playerLook.dot(toWarden);
        return dotProduct > 0.8; // About 36 degree cone
    }

    private void activateFearLock(Player player) {
        fearLockedPlayers.add(player);
        fearLockTimers.put(player, -FEAR_LOCK_COOLDOWN); // Set cooldown

        // Lift player 20 blocks up
        player.setDeltaMovement(0, 2.0, 0); // Strong upward velocity

        // Schedule the throw after a short delay
        this.level().scheduleTick(this.blockPosition(), net.minecraft.world.level.block.Blocks.AIR, 30); // 1.5 seconds

        // Play fear sound
        this.playSound(SoundEvents.ENDER_DRAGON_GROWL, 2.0F, 0.5F);

        // Create a delayed task to throw the player
        new java.util.Timer().schedule(new java.util.TimerTask() {
            @Override
            public void run() {
                if (player.isAlive() && player.distanceTo(AIWardenEntity.this) < 30.0) {
                    throwPlayer(player);
                }
            }
        }, 1500); // 1.5 seconds delay
    }

    private void throwPlayer(Player player) {
        // Calculate throw direction (away from warden)
        double deltaX = player.getX() - this.getX();
        double deltaZ = player.getZ() - this.getZ();
        double distance = Math.sqrt(deltaX * deltaX + deltaZ * deltaZ);

        if (distance > 0) {
            // Throw player horizontally with great force
            double throwStrength = 3.0;
            player.setDeltaMovement(
                (deltaX / distance) * throwStrength,
                0.5, // Slight upward component
                (deltaZ / distance) * throwStrength
            );

            // Play throw sound
            this.playSound(SoundEvents.GENERIC_EXPLODE, 1.5F, 1.2F);

            // Create particle effect at player location
            if (this.level() instanceof net.minecraft.server.level.ServerLevel serverLevel) {
                serverLevel.sendParticles(
                    net.minecraft.core.particles.ParticleTypes.EXPLOSION,
                    player.getX(), player.getY(), player.getZ(),
                    10, 1.0, 1.0, 1.0, 0.1
                );
            }
        }
    }

    @Override
    protected SoundEvent getAmbientSound() {
        return SoundEvents.WARDEN_AMBIENT;
    }

    @Override
    protected SoundEvent getHurtSound(DamageSource damageSource) {
        return SoundEvents.WARDEN_HURT;
    }

    @Override
    protected SoundEvent getDeathSound() {
        return SoundEvents.WARDEN_DEATH;
    }

    @Override
    protected void playStepSound(BlockPos pos, BlockState blockState) {
        this.playSound(SoundEvents.WARDEN_STEP, 0.15F, 1.0F);
    }

    @Override
    public boolean removeWhenFarAway(double distanceToClosestPlayer) {
        return false; // AI Warden doesn't despawn
    }

    @Override
    public boolean canBeLeashed(Player player) {
        return false; // Cannot be leashed
    }

    // Completely disable darkness/blindness effects
    @Override
    public boolean doHurtTarget(net.minecraft.world.entity.Entity entity) {
        // Custom hurt target without darkness effects
        if (entity instanceof LivingEntity livingEntity) {
            boolean result = livingEntity.hurt(this.damageSources().mobAttack(this), (float)this.getAttributeValue(Attributes.ATTACK_DAMAGE));
            // Don't apply any darkness/blindness effects - just return the damage result
            return result;
        }
        return false;
    }

    // Override brain to use simple goal-based AI instead of complex brain behaviors
    @Override
    protected net.minecraft.world.entity.ai.Brain<?> makeBrain(com.mojang.serialization.Dynamic<?> dynamic) {
        // Just use parent brain but we'll override the step method
        return super.makeBrain(dynamic);
    }

    // Disable brain updates to rely on goal-based AI
    @Override
    protected void customServerAiStep() {
        // Skip brain updates and use goal-based AI only
        // Don't call super.customServerAiStep() to avoid brain behaviors
    }

    // Disable sonic boom darkness
    @Override
    public void handleEntityEvent(byte eventId) {
        // Handle events but skip darkness-related ones
        if (eventId != 61 && eventId != 62) { // Skip darkness event IDs
            super.handleEntityEvent(eventId);
        }
    }
}
