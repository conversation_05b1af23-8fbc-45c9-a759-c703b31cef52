package com.aiwardenmod.entity.custom;

import com.aiwardenmod.entity.ai.AerialSlamGoal;
import com.aiwardenmod.entity.ai.AggressiveAttackGoal;
import com.aiwardenmod.entity.ai.AggressiveChaseGoal;
import com.aiwardenmod.entity.ai.BuildBlocksToTargetGoal;
import com.aiwardenmod.entity.ai.PersistentChaseGoal;
import com.aiwardenmod.entity.ai.WardenStyleAttackGoal;
import com.aiwardenmod.entity.ai.EnhancedChaseGoal;
import com.aiwardenmod.entity.ai.FlyToTargetGoal;
import com.aiwardenmod.entity.ai.JumpToTargetGoal;
import com.aiwardenmod.entity.ai.SilentTrackingGoal;
import net.minecraft.core.BlockPos;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.ai.attributes.AttributeSupplier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.goal.*;
import net.minecraft.world.entity.ai.goal.target.HurtByTargetGoal;
import net.minecraft.world.entity.ai.goal.target.NearestAttackableTargetGoal;
import net.minecraft.world.entity.monster.Monster;
import net.minecraft.world.entity.monster.warden.Warden;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.block.state.BlockState;
import org.jetbrains.annotations.Nullable;

public class AIWardenEntity extends Warden {
    private int blockBuildCooldown = 0;
    private int jumpCooldown = 0;
    private int flyCooldown = 0;
    private boolean isFlying = false;
    private int flyingTicks = 0;
    private LivingEntity flyTarget = null;
    private boolean attackMode = false;

    // Command-triggered special abilities
    private boolean commandTriggeredSlam = false;
    private boolean commandTriggeredTeleportStrike = false;
    private boolean commandTriggeredEarthquake = false;
    private int earthquakeIntensity = 5;

    // Command-controlled attributes
    private double damageMultiplier = 1.0;
    private double speedMultiplier = 1.0;
    private boolean inRageMode = false;
    private int rageModeTimer = 0;
    private boolean buildingEnabled = true; // Building toggle - enabled by default

    // Stuck detection and recovery
    private BlockPos lastPosition = null;
    private int stuckCounter = 0;
    private static final int MAX_STUCK_TIME = 60; // 3 seconds of being stuck

    private static final int MAX_BUILD_COOLDOWN = 1; // 0.05 seconds - ULTRA fast building
    private static final int MAX_JUMP_COOLDOWN = 40; // 2 seconds at 20 ticks per second
    private static final int MAX_FLY_COOLDOWN = 400; // 20 seconds - much less frequent flying
    private static final int FLY_DURATION = 60; // 3 seconds of flying
    // Removed fear lock system - now using commands instead per player

    public AIWardenEntity(EntityType<? extends Warden> entityType, Level level) {
        super(entityType, level);
        this.xpReward = 50; // Same as regular Warden
    }

    public static AttributeSupplier.Builder createAttributes() {
        return Warden.createAttributes()
                .add(Attributes.MAX_HEALTH, 500.0D) // Same as Warden
                .add(Attributes.MOVEMENT_SPEED, 0.3D) // Slightly faster than Warden
                .add(Attributes.KNOCKBACK_RESISTANCE, 1.0D) // Immune to knockback
                .add(Attributes.ATTACK_KNOCKBACK, 1.5D)
                .add(Attributes.ATTACK_DAMAGE, 15.0D) // Half damage of regular Warden
                .add(Attributes.FOLLOW_RANGE, 150.0D); // MASSIVE follow range - 150 blocks
    }

    @Override
    protected void registerGoals() {
        // Don't call super to avoid Warden's complex goals

        // COMBAT-FOCUSED GOAL PRIORITY SYSTEM
        this.goalSelector.addGoal(0, new FloatGoal(this));
        this.goalSelector.addGoal(1, new WardenStyleAttackGoal(this)); // HIGHEST PRIORITY - Warden-style aggressive attacks when close
        this.goalSelector.addGoal(2, new AggressiveChaseGoal(this)); // Aggressive chase when at same level and medium range
        this.goalSelector.addGoal(3, new MeleeAttackGoal(this, 1.3D, true)); // Enhanced melee attack when very close
        this.goalSelector.addGoal(4, new PersistentChaseGoal(this)); // Persistent long-range chase like default Warden
        this.goalSelector.addGoal(5, new BuildBlocksToTargetGoal(this, 1.0D)); // LOWER PRIORITY - Only when really needed
        this.goalSelector.addGoal(6, new net.minecraft.world.entity.ai.goal.MoveTowardsTargetGoal(this, 1.2D, 32.0F)); // Fallback movement
        this.goalSelector.addGoal(7, new WaterAvoidingRandomStrollGoal(this, 0.6D));
        this.goalSelector.addGoal(8, new LookAtPlayerGoal(this, Player.class, 8.0F));
        this.goalSelector.addGoal(9, new RandomLookAroundGoal(this));

        // Enhanced target selectors - work with our tracking system
        this.targetSelector.addGoal(1, new HurtByTargetGoal(this));
        this.targetSelector.addGoal(2, new NearestAttackableTargetGoal<>(this, Player.class, 10, true, false, null) {
            @Override
            public boolean canUse() {
                // Let our enhanced tracking system handle target acquisition
                // This goal only activates if we don't have a target from tracking
                return AIWardenEntity.this.getTarget() == null && super.canUse();
            }
        });
    }

    @Override
    public void tick() {
        super.tick();

        // Decrease cooldowns
        if (blockBuildCooldown > 0) {
            blockBuildCooldown--;
        }
        if (jumpCooldown > 0) {
            jumpCooldown--;
        }
        if (flyCooldown > 0) {
            flyCooldown--;
        }

        // Handle flying behavior
        if (isFlying) {
            handleFlying();
        }

        // Handle rage mode timer
        if (this.rageModeTimer > 0) {
            this.rageModeTimer--;
            if (this.rageModeTimer <= 0) {
                this.inRageMode = false;
                System.out.println("AI Warden: Rage mode ended");
            }
        }

        // ENHANCED AGGRESSIVE SPEED SYSTEM
        if (this.tickCount % 20 == 0) { // Update every second
            double baseSpeed = 0.6D; // MUCH FASTER base movement speed (was 0.3D)

            // Speed boost when hunting
            if (this.isHunting) {
                baseSpeed += 0.3D; // Extra speed when hunting
            }

            // Speed boost when close to target
            if (this.getTarget() != null && this.distanceTo(this.getTarget()) < 15.0) {
                baseSpeed += 0.4D; // Extra speed when close to target
            }

            double newSpeed = baseSpeed * this.speedMultiplier;
            this.getAttribute(Attributes.MOVEMENT_SPEED).setBaseValue(newSpeed);
        }

        // Enhanced tracking system - runs before other systems
        enhancedPlayerTracking();

        // Random special ability system
        if (this.specialAbilityCooldown > 0) {
            this.specialAbilityCooldown--;
        } else if (this.getTarget() != null && this.random.nextFloat() < 0.05F) { // 5% chance per tick when target exists
            triggerRandomSpecialAbility();
            this.specialAbilityCooldown = SPECIAL_ABILITY_INTERVAL;
        }

        // Check if we're stuck in blocks and fix it
        if (this.tickCount % 10 == 0) { // Check every 0.5 seconds
            checkAndFixStuckInBlocks();
            checkStuckInPosition();
        }

        // ULTRA-ENHANCED player tracking and darkness removal
        if (this.level() instanceof net.minecraft.server.level.ServerLevel) {
            java.util.List<Player> nearbyPlayers = this.level().getEntitiesOfClass(Player.class,
                this.getBoundingBox().inflate(200.0)); // ULTRA MASSIVE tracking radius - 200 blocks!

            // Remove darkness effects
            for (Player player : nearbyPlayers) {
                if (player.hasEffect(net.minecraft.world.effect.MobEffects.DARKNESS)) {
                    player.removeEffect(net.minecraft.world.effect.MobEffects.DARKNESS);
                    System.out.println("AI Warden: Removed darkness effect from " + player.getName().getString());
                }
            }

            // Enhanced player tracking - always try to find a target
            if (this.getTarget() == null && this.tickCount % 10 == 0) { // Check every 0.5 seconds
                Player nearestPlayer = findNearestPlayer();
                if (nearestPlayer != null) {
                    this.setTarget(nearestPlayer);
                    System.out.println("AI Warden: Acquired new target: " + nearestPlayer.getName().getString());
                }
            }
        }

        // Debug output every 2 seconds
        if (this.tickCount % 40 == 0 && this.getTarget() != null) {
            LivingEntity target = this.getTarget();
            double heightDiff = target.getY() - this.getY();
            double distance = this.distanceTo(target);
            System.out.println("AI Warden Debug - Target: " + target.getName().getString() +
                             ", Height diff: " + String.format("%.1f", heightDiff) +
                             ", Distance: " + String.format("%.1f", distance) +
                             ", Has target: " + (this.getTarget() != null));
        }
    }

    public boolean canBuildBlocks() {
        return blockBuildCooldown <= 0;
    }

    public boolean canJump() {
        return jumpCooldown <= 0;
    }

    public boolean canFly() {
        return flyCooldown <= 0 && !isFlying;
    }

    public void setBlockBuildCooldown() {
        this.blockBuildCooldown = MAX_BUILD_COOLDOWN;
    }

    public void setJumpCooldown() {
        this.jumpCooldown = MAX_JUMP_COOLDOWN;
    }

    public void setFlyCooldown() {
        this.flyCooldown = MAX_FLY_COOLDOWN;
    }

    public void setAttackMode(boolean attackMode) {
        this.attackMode = attackMode;
        if (attackMode) {
            System.out.println("AI Warden: Attack mode activated!");
        }
    }

    public boolean isInAttackMode() {
        return this.attackMode;
    }

    private Player findNearestPlayer() {
        // ULTRA-ENHANCED player detection - find players from extremely far away
        java.util.List<Player> players = this.level().getEntitiesOfClass(Player.class,
            this.getBoundingBox().inflate(200.0)); // ULTRA MASSIVE detection radius - 200 blocks!

        Player nearestPlayer = null;
        double nearestDistance = Double.MAX_VALUE;

        for (Player player : players) {
            if (player.isSpectator() || player.isCreative()) {
                continue;
            }

            double distance = this.distanceTo(player);
            if (distance < nearestDistance) {
                nearestDistance = distance;
                nearestPlayer = player;
            }
        }

        return nearestPlayer;
    }

    public boolean tryBuildBlock(BlockPos pos) {
        if (!canBuildBlocks() || !level().isEmptyBlock(pos)) {
            System.out.println("AI Warden: Cannot build - cooldown: " + !canBuildBlocks() + ", empty: " + !level().isEmptyBlock(pos));
            return false;
        }

        // Check if the position is reasonable for building
        if (pos.getY() > this.getY() + 5 || pos.getY() < this.getY() - 2) {
            System.out.println("AI Warden: Position not reasonable for building: " + pos);
            return false;
        }

        // MINIMAL SAFETY: Only prevent building in exact Warden position
        BlockPos wardenPos = this.blockPosition();
        BlockPos wardenHead = wardenPos.above();

        if (pos.equals(wardenPos) || pos.equals(wardenHead)) {
            System.out.println("AI Warden: Cannot build where Warden is standing: " + pos);
            return false;
        }

        // Allow all other building - stuck detection will handle any issues

        // Place a dirt block
        BlockState blockState = Blocks.DIRT.defaultBlockState();
        level().setBlock(pos, blockState, 3);
        setBlockBuildCooldown();

        // Smart positioning after building
        handlePostBuildMovement(pos);

        // Play sound effect
        level().playSound(null, pos, SoundEvents.GRAVEL_PLACE, this.getSoundSource(), 1.0F, 1.0F);

        System.out.println("AI Warden: Successfully built block at " + pos + " with smart positioning");
        return true;
    }

    private void handlePostBuildMovement(BlockPos builtPos) {
        BlockPos wardenPos = this.blockPosition();

        // SIMPLE: If we built under ourselves, just teleport up
        if (builtPos.equals(wardenPos.below())) {
            this.teleportTo(this.getX(), builtPos.getY() + 1.0, this.getZ());
            System.out.println("AI Warden: Teleported up after building under self");
            return;
        }

        // For other builds, just give a small upward boost if it's higher
        if (builtPos.getY() > wardenPos.getY()) {
            this.setDeltaMovement(this.getDeltaMovement().add(0, 0.2, 0));
            System.out.println("AI Warden: Small boost after building higher block");
        }
    }

    public boolean tryJumpToTarget(LivingEntity target) {
        if (!canJump() || target == null) {
            return false;
        }

        double deltaX = target.getX() - this.getX();
        double deltaZ = target.getZ() - this.getZ();
        double deltaY = target.getY() - this.getY();

        // Only jump if target is higher and within reasonable range
        if (deltaY > 0.5 && deltaY < 4.0) {
            double distance = Math.sqrt(deltaX * deltaX + deltaZ * deltaZ);
            if (distance < 8.0 && distance > 2.0) {
                // Calculate jump velocity
                double jumpStrength = Math.min(0.8, deltaY * 0.3 + 0.4);
                this.setDeltaMovement(deltaX * 0.1, jumpStrength, deltaZ * 0.1);
                setJumpCooldown();

                // Play jump sound
                this.playSound(SoundEvents.RAVAGER_STEP, 1.0F, 0.8F);
                return true;
            }
        }

        return false;
    }

    public boolean tryFlyToTarget(LivingEntity target) {
        if (!canFly() || target == null) {
            return false;
        }

        double heightDifference = target.getY() - this.getY();
        double horizontalDistance = Math.sqrt(
            Math.pow(target.getX() - this.getX(), 2) +
            Math.pow(target.getZ() - this.getZ(), 2)
        );

        // Only fly if target is significantly higher and within range
        if (heightDifference > 4.0 && horizontalDistance < 20.0) {
            startFlying(target);
            return true;
        }

        return false;
    }

    private void startFlying(LivingEntity target) {
        this.isFlying = true;
        this.flyingTicks = 0;
        this.flyTarget = target;
        this.setNoGravity(true);

        // Initial upward velocity
        this.setDeltaMovement(this.getDeltaMovement().add(0, 0.8, 0));

        // Play flying sound
        this.playSound(SoundEvents.ENDER_DRAGON_FLAP, 1.0F, 0.8F);

        setFlyCooldown();
    }

    private void handleFlying() {
        flyingTicks++;

        if (flyTarget == null || !flyTarget.isAlive() || flyingTicks > FLY_DURATION) {
            stopFlying();
            return;
        }

        // Calculate direction to target
        double deltaX = flyTarget.getX() - this.getX();
        double deltaY = flyTarget.getY() - this.getY();
        double deltaZ = flyTarget.getZ() - this.getZ();

        double distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY + deltaZ * deltaZ);

        if (distance < 2.0) {
            // Close enough to smash down
            performSmashAttack();
            return;
        }

        // Move towards target
        double speed = 0.3;
        this.setDeltaMovement(
            deltaX / distance * speed,
            Math.max(deltaY / distance * speed, -0.5), // Don't fall too fast
            deltaZ / distance * speed
        );

        // Play flying sounds occasionally
        if (flyingTicks % 20 == 0) {
            this.playSound(SoundEvents.PHANTOM_FLAP, 0.5F, 1.2F);
        }
    }

    private void performSmashAttack() {
        // Create a powerful downward attack
        this.setDeltaMovement(0, -1.5, 0); // Fast downward movement

        // Deal damage to nearby entities when landing
        if (this.onGround() || this.getY() <= flyTarget.getY() + 1) {
            // Create explosion-like effect
            this.level().explode(this, this.getX(), this.getY(), this.getZ(), 2.0F, Level.ExplosionInteraction.NONE);

            // Damage nearby entities with COMMAND-CONTROLLED DAMAGE
            for (LivingEntity entity : this.level().getEntitiesOfClass(LivingEntity.class,
                    this.getBoundingBox().inflate(3.0))) {
                if (entity != this && entity instanceof Player) {
                    float baseDamage = 20.0F; // Original smash damage
                    float reducedDamage = baseDamage * 0.5F; // 50% base reduction
                    float finalDamage = reducedDamage * (float)this.damageMultiplier; // Apply command multiplier

                    // Rage mode bonus
                    if (this.inRageMode) {
                        finalDamage *= 1.5F;
                    }

                    entity.hurt(this.damageSources().mobAttack(this), finalDamage);

                    // Reduced knockback effect (also affected by rage mode)
                    double deltaX = entity.getX() - this.getX();
                    double deltaZ = entity.getZ() - this.getZ();
                    double distance = Math.sqrt(deltaX * deltaX + deltaZ * deltaZ);
                    if (distance > 0) {
                        float knockbackMultiplier = this.inRageMode ? 1.0F : 0.75F;
                        entity.setDeltaMovement(
                            deltaX / distance * knockbackMultiplier,
                            this.inRageMode ? 0.4 : 0.25,
                            deltaZ / distance * knockbackMultiplier
                        );
                    }

                    String rageText = this.inRageMode ? " [RAGE MODE]" : "";
                    System.out.println("AI Warden: Smash attack hit " + entity.getName().getString() + " for " + finalDamage + " damage (Base: " + baseDamage + ", Multiplier: " + this.damageMultiplier + "x" + rageText + ")");
                }
            }

            // Play impact sound
            this.playSound(SoundEvents.GENERIC_EXPLODE, 1.0F, 0.8F);

            stopFlying();
        }
    }

    private void stopFlying() {
        this.isFlying = false;
        this.flyingTicks = 0;
        this.flyTarget = null;
        this.setNoGravity(false);
    }

    // Fear Lock system removed - now using commands for special abilities

    // Command-triggered special abilities
    public void triggerAerialSlam() {
        this.commandTriggeredSlam = true;
        System.out.println("AI Warden: Aerial slam triggered by command!");
    }

    public boolean isCommandTriggeredSlam() {
        return this.commandTriggeredSlam;
    }

    public void resetCommandTriggeredSlam() {
        this.commandTriggeredSlam = false;
    }

    public void triggerTeleportStrike() {
        this.commandTriggeredTeleportStrike = true;
        System.out.println("AI Warden: Teleport strike triggered by command!");
    }

    public boolean isCommandTriggeredTeleportStrike() {
        return this.commandTriggeredTeleportStrike;
    }

    public void resetCommandTriggeredTeleportStrike() {
        this.commandTriggeredTeleportStrike = false;
    }

    public void triggerEarthquake(int intensity) {
        this.commandTriggeredEarthquake = true;
        this.earthquakeIntensity = intensity;
        System.out.println("AI Warden: Earthquake triggered by command with intensity " + intensity + "!");
    }

    public boolean isCommandTriggeredEarthquake() {
        return this.commandTriggeredEarthquake;
    }

    public int getEarthquakeIntensity() {
        return this.earthquakeIntensity;
    }

    public void resetCommandTriggeredEarthquake() {
        this.commandTriggeredEarthquake = false;
    }

    public void triggerRageMode(int durationSeconds) {
        this.inRageMode = true;
        this.rageModeTimer = durationSeconds * 20; // Convert to ticks
        System.out.println("AI Warden: Rage mode activated for " + durationSeconds + " seconds!");
    }

    public boolean isInRageMode() {
        return this.inRageMode;
    }

    // Damage and speed control
    public void setDamageMultiplier(double multiplier) {
        this.damageMultiplier = multiplier;
        System.out.println("AI Warden: Damage multiplier set to " + multiplier + "x");
    }

    public double getDamageMultiplier() {
        return this.damageMultiplier;
    }

    public void setSpeedMultiplier(double multiplier) {
        this.speedMultiplier = multiplier;
        System.out.println("AI Warden: Speed multiplier set to " + multiplier + "x");
    }

    public double getSpeedMultiplier() {
        return this.speedMultiplier;
    }

    // Building toggle control
    public void setBuildingEnabled(boolean enabled) {
        this.buildingEnabled = enabled;
        System.out.println("AI Warden: Building " + (enabled ? "ENABLED" : "DISABLED"));
    }

    public boolean isBuildingEnabled() {
        return this.buildingEnabled;
    }

    // Random special ability system
    private void triggerRandomSpecialAbility() {
        if (this.getTarget() == null) {
            return;
        }

        // Equal chance for each ability (25% each)
        int abilityChoice = this.random.nextInt(4);

        switch (abilityChoice) {
            case 0:
                triggerAerialSlam();
                System.out.println("AI Warden: Randomly triggered AERIAL SLAM!");
                break;
            case 1:
                triggerTeleportStrike();
                System.out.println("AI Warden: Randomly triggered TELEPORT STRIKE!");
                break;
            case 2:
                triggerEarthquake(this.random.nextInt(5) + 3); // Intensity 3-7
                System.out.println("AI Warden: Randomly triggered EARTHQUAKE!");
                break;
            case 3:
                triggerRageMode(30 + this.random.nextInt(31)); // 30-60 seconds
                System.out.println("AI Warden: Randomly triggered RAGE MODE!");
                break;
        }
    }

    // ULTRA-ENHANCED AGGRESSIVE TRACKING SYSTEM
    private void enhancedPlayerTracking() {
        this.trackingTimer++;
        this.aggressiveSearchTimer++;

        // Current target check
        LivingEntity currentTarget = this.getTarget();

        // If we have a target, update tracking info and enter hunting mode
        if (currentTarget instanceof Player player) {
            this.lastKnownTarget = player;
            this.lastKnownTargetPos = player.blockPosition();
            this.lostTargetTimer = 0;
            this.isHunting = true;
            this.huntingIntensity = Math.min(this.huntingIntensity + 1, 200); // Build up intensity

            // AGGRESSIVE: Constantly update target position even when we have them
            if (this.trackingTimer % 2 == 0) { // Every 2 ticks
                this.lastKnownTargetPos = player.blockPosition();
            }
            return; // We have a target, but keep tracking aggressively
        }

        // No current target - ULTRA-AGGRESSIVE DETECTION
        this.lostTargetTimer++;

        // INSTANT SOUND DETECTION - check EVERY TICK
        Player detectedPlayer = ultraAggressiveSoundDetection();
        if (detectedPlayer != null) {
            acquireTarget(detectedPlayer, "ULTRA-SOUND");
            return;
        }

        // MOVEMENT DETECTION - check every 2 ticks (was 5)
        if (this.trackingTimer % 2 == 0) {
            Player movingPlayer = ultraAggressiveMovementDetection();
            if (movingPlayer != null) {
                acquireTarget(movingPlayer, "ULTRA-MOVEMENT");
                return;
            }
        }

        // PRESENCE DETECTION - check every 5 ticks (was 20)
        if (this.trackingTimer % 5 == 0) {
            Player nearbyPlayer = ultraAggressivePresenceDetection();
            if (nearbyPlayer != null) {
                acquireTarget(nearbyPlayer, "ULTRA-PRESENCE");
                return;
            }
        }

        // AGGRESSIVE INVESTIGATION - start immediately when target lost
        if (this.lostTargetTimer > 10 && this.lastKnownTargetPos != null) { // 0.5 seconds (was 5)
            performAggressiveInvestigation();
        }

        // HUNTING MODE - expand search when in hunting mode
        if (this.isHunting && this.lostTargetTimer > 20) { // 1 second
            performHuntingBehavior();
        }

        // RESET hunting mode if no target for too long
        if (this.lostTargetTimer > 400) { // 20 seconds
            this.isHunting = false;
            this.huntingIntensity = 0;
            System.out.println("AI Warden: Exiting hunting mode - no targets found");
        }
    }

    private void acquireTarget(Player player, String method) {
        this.setTarget(player);
        this.lastKnownTarget = player;
        this.lastKnownTargetPos = player.blockPosition();
        this.lostTargetTimer = 0;
        this.isHunting = true;
        this.huntingIntensity = Math.min(this.huntingIntensity + 50, 200);

        double distance = this.distanceTo(player);
        System.out.println("AI Warden: " + method + " DETECTION! Target acquired at " + String.format("%.1f", distance) + " blocks! Hunting intensity: " + this.huntingIntensity);
    }

    private Player ultraAggressiveSoundDetection() {
        // ULTRA-AGGRESSIVE sound detection - 300 block range, detects EVERYTHING
        for (Player player : this.level().getEntitiesOfClass(Player.class,
                this.getBoundingBox().inflate(this.soundDetectionRange))) {

            if (player.isSpectator() || player.isCreative()) {
                continue;
            }

            // ULTRA-SENSITIVE noise detection
            boolean makingNoise = false;

            // ANY movement = noise (much more sensitive)
            if (player.getDeltaMovement().lengthSqr() > 0.0001) { // Was 0.01
                makingNoise = true;
            }

            // Breathing while moving = noise
            if (player.onGround() && player.getDeltaMovement().horizontalDistanceSqr() > 0.00001) { // Was 0.001
                makingNoise = true;
            }

            // ANY vertical movement = noise
            if (Math.abs(player.getDeltaMovement().y) > 0.001) {
                makingNoise = true;
            }

            // Inventory actions = noise
            if (player.isUsingItem() || player.getMainHandItem().getCount() != player.getMainHandItem().getMaxStackSize()) {
                makingNoise = true;
            }

            // Health changes = noise (getting hurt, healing)
            if (player.getHealth() < player.getMaxHealth()) {
                makingNoise = true;
            }

            if (makingNoise) {
                return player;
            }
        }
        return null;
    }

    private Player ultraAggressiveMovementDetection() {
        // ULTRA-AGGRESSIVE movement detection - 250 block range, detects micro-movements
        double range = 250.0 + (this.huntingIntensity * 2.0); // Range increases with hunting intensity

        for (Player player : this.level().getEntitiesOfClass(Player.class,
                this.getBoundingBox().inflate(range))) {

            if (player.isSpectator() || player.isCreative()) {
                continue;
            }

            // ULTRA-SENSITIVE movement detection
            if (player.getDeltaMovement().lengthSqr() > 0.00001) { // Detects tiny movements
                return player;
            }

            // Position change detection (even if not moving this tick)
            if (this.lastKnownTarget == player && this.lastKnownTargetPos != null) {
                double positionChange = player.blockPosition().distSqr(this.lastKnownTargetPos);
                if (positionChange > 0.1) { // Moved even slightly
                    return player;
                }
            }
        }
        return null;
    }

    private Player ultraAggressivePresenceDetection() {
        // ULTRA-AGGRESSIVE presence detection - MASSIVE range, detects existence
        double baseRange = 300.0;
        double huntingBonus = this.huntingIntensity * 3.0; // Up to 600 extra blocks when hunting
        double totalRange = baseRange + huntingBonus;

        for (Player player : this.level().getEntitiesOfClass(Player.class,
                this.getBoundingBox().inflate(totalRange))) {

            if (player.isSpectator() || player.isCreative()) {
                continue;
            }

            // PRESENCE DETECTION - just existing is enough
            return player; // Found any player in massive range
        }
        return null;
    }

    private void performAggressiveInvestigation() {
        if (this.lastKnownTargetPos == null) {
            return;
        }

        double distanceToLastPos = this.blockPosition().distSqr(this.lastKnownTargetPos);

        if (distanceToLastPos > 2.0) { // More than 1.4 blocks away
            // AGGRESSIVE navigation to last known position
            this.getNavigation().moveTo(this.lastKnownTargetPos.getX(), this.lastKnownTargetPos.getY(), this.lastKnownTargetPos.getZ(), 1.5);

            if (this.aggressiveSearchTimer % 20 == 0) { // Every second
                System.out.println("AI Warden: AGGRESSIVELY investigating last known position at " + this.lastKnownTargetPos + "!");
            }
        } else {
            // At last known position - expand search
            performExpandedSearch();
        }
    }

    private void performHuntingBehavior() {
        // HUNTING MODE - actively search in expanding circles
        if (this.aggressiveSearchTimer % 40 == 0) { // Every 2 seconds
            double searchRadius = 10.0 + (this.huntingIntensity * 0.5); // Expanding search

            // Random search around last known position
            if (this.lastKnownTargetPos != null) {
                double searchX = this.lastKnownTargetPos.getX() + (this.random.nextDouble() - 0.5) * searchRadius * 2;
                double searchZ = this.lastKnownTargetPos.getZ() + (this.random.nextDouble() - 0.5) * searchRadius * 2;

                this.getNavigation().moveTo(searchX, this.lastKnownTargetPos.getY(), searchZ, 1.3);
                System.out.println("AI Warden: HUNTING MODE - expanding search! Intensity: " + this.huntingIntensity + ", Radius: " + String.format("%.1f", searchRadius));
            }
        }
    }

    private void performExpandedSearch() {
        // Search in a pattern around current position
        if (this.aggressiveSearchTimer % 30 == 0) { // Every 1.5 seconds
            double searchRadius = 8.0;
            double angle = (this.aggressiveSearchTimer / 30.0) * Math.PI * 0.5; // Spiral pattern

            double searchX = this.getX() + Math.cos(angle) * searchRadius;
            double searchZ = this.getZ() + Math.sin(angle) * searchRadius;

            this.getNavigation().moveTo(searchX, this.getY(), searchZ, 1.2);
            System.out.println("AI Warden: Expanded search pattern - looking for traces!");
        }
    }

    private void checkAndFixStuckInBlocks() {
        // Check if the warden is stuck inside blocks
        BlockPos currentPos = this.blockPosition();
        BlockPos headPos = currentPos.above();

        // If we're inside solid blocks, try to get out
        if (!this.level().isEmptyBlock(currentPos) || !this.level().isEmptyBlock(headPos)) {
            System.out.println("AI Warden: Stuck in blocks! Attempting to escape...");

            // Try to find a nearby empty space
            for (int x = -1; x <= 1; x++) {
                for (int z = -1; z <= 1; z++) {
                    for (int y = 0; y <= 2; y++) {
                        BlockPos escapePos = currentPos.offset(x, y, z);
                        BlockPos escapeHead = escapePos.above();

                        if (this.level().isEmptyBlock(escapePos) && this.level().isEmptyBlock(escapeHead)) {
                            // Found a good escape position
                            this.teleportTo(escapePos.getX() + 0.5, escapePos.getY(), escapePos.getZ() + 0.5);
                            System.out.println("AI Warden: Escaped to " + escapePos);
                            return;
                        }
                    }
                }
            }

            // If no escape found, just teleport up
            this.teleportTo(this.getX(), this.getY() + 2, this.getZ());
            System.out.println("AI Warden: Teleported up to escape blocks");
        }
    }

    private void checkStuckInPosition() {
        BlockPos currentPos = this.blockPosition();

        // Check if we're in the same position as last check
        if (this.lastPosition != null && this.lastPosition.equals(currentPos)) {
            this.stuckCounter++;

            if (this.stuckCounter >= MAX_STUCK_TIME) {
                System.out.println("AI Warden: Stuck in same position for too long! Attempting recovery...");
                performStuckRecovery();
                this.stuckCounter = 0;
            }
        } else {
            // We moved, reset counter
            this.stuckCounter = 0;
        }

        this.lastPosition = currentPos;
    }

    private void performStuckRecovery() {
        BlockPos currentPos = this.blockPosition();

        // Strategy 1: Try to teleport to a safe nearby position
        for (int radius = 1; radius <= 3; radius++) {
            for (int x = -radius; x <= radius; x++) {
                for (int z = -radius; z <= radius; z++) {
                    if (x == 0 && z == 0) continue; // Skip current position

                    BlockPos testPos = currentPos.offset(x, 0, z);
                    BlockPos testHead = testPos.above();

                    if (this.level().isEmptyBlock(testPos) && this.level().isEmptyBlock(testHead)) {
                        this.teleportTo(testPos.getX() + 0.5, testPos.getY(), testPos.getZ() + 0.5);
                        System.out.println("AI Warden: Recovered by teleporting to " + testPos);
                        return;
                    }
                }
            }
        }

        // Strategy 2: Teleport up if no horizontal space found
        this.teleportTo(this.getX(), this.getY() + 3, this.getZ());
        System.out.println("AI Warden: Recovered by teleporting up 3 blocks");
    }

    @Override
    protected SoundEvent getAmbientSound() {
        return SoundEvents.WARDEN_AMBIENT;
    }

    @Override
    protected SoundEvent getHurtSound(DamageSource damageSource) {
        return SoundEvents.WARDEN_HURT;
    }

    @Override
    protected SoundEvent getDeathSound() {
        return SoundEvents.WARDEN_DEATH;
    }

    @Override
    protected void playStepSound(BlockPos pos, BlockState blockState) {
        this.playSound(SoundEvents.WARDEN_STEP, 0.15F, 1.0F);
    }

    @Override
    public boolean removeWhenFarAway(double distanceToClosestPlayer) {
        return false; // AI Warden doesn't despawn
    }

    @Override
    public boolean canBeLeashed(Player player) {
        return false; // Cannot be leashed
    }



    // Use normal brain but override behaviors to prevent sonic boom
    @Override
    protected net.minecraft.world.entity.ai.Brain<?> makeBrain(com.mojang.serialization.Dynamic<?> dynamic) {
        // Use parent brain but we'll override the step method to control it
        return super.makeBrain(dynamic);
    }

    // Override to prevent sonic boom while keeping basic functionality
    @Override
    protected void customServerAiStep() {
        // Call parent but intercept any sonic boom attempts
        try {
            // Use parent AI but we'll block sonic boom in handleEntityEvent
            super.customServerAiStep();
        } catch (Exception e) {
            // If parent AI causes issues, fall back to basic goal system
            System.out.println("AI Warden: Parent AI failed, using fallback goal system");
            this.goalSelector.tick();
            this.targetSelector.tick();
            this.getNavigation().tick();
            this.getMoveControl().tick();
            this.getLookControl().tick();
            this.getJumpControl().tick();
        }
    }

    // Random special ability system
    private int specialAbilityCooldown = 0;
    private static final int SPECIAL_ABILITY_INTERVAL = 200; // 10 seconds between abilities

    // ULTRA-ENHANCED tracking system
    private Player lastKnownTarget = null;
    private BlockPos lastKnownTargetPos = null;
    private int trackingTimer = 0;
    private int soundDetectionRange = 300; // MASSIVE 300 block sound detection
    private int lostTargetTimer = 0;
    private int aggressiveSearchTimer = 0;
    private boolean isHunting = false;
    private int huntingIntensity = 0;

    // Override sonic boom and special attacks to use damage multiplier
    @Override
    public void handleEntityEvent(byte eventId) {
        if (eventId == 4) { // Sonic boom event
            // Reduce sonic boom frequency - only 20% chance
            if (this.random.nextFloat() < 0.2F) {
                performCustomSonicBoom();
            } else {
                System.out.println("AI Warden: Sonic boom suppressed (reduced frequency)");
            }
        } else {
            // Block other special events to prevent unwanted behaviors
            System.out.println("AI Warden: Blocked entity event " + eventId + " (preventing unwanted behaviors)");
        }
    }

    // Override all darkness-related methods with COMMAND-CONTROLLED DAMAGE
    @Override
    public boolean doHurtTarget(net.minecraft.world.entity.Entity entity) {
        // Custom attack without any darkness effects and COMMAND-CONTROLLED DAMAGE
        if (entity instanceof LivingEntity livingEntity) {
            float baseDamage = (float)this.getAttributeValue(Attributes.ATTACK_DAMAGE);
            float reducedDamage = baseDamage * 0.5F; // 50% base reduction
            float finalDamage = reducedDamage * (float)this.damageMultiplier; // Apply command multiplier

            // Rage mode bonus
            if (this.inRageMode) {
                finalDamage *= 1.5F; // 50% bonus damage in rage mode
            }

            boolean result = livingEntity.hurt(this.damageSources().mobAttack(this), finalDamage);

            if (result) {
                // Add knockback (also affected by rage mode)
                double knockbackStrength = this.getAttributeValue(Attributes.ATTACK_KNOCKBACK);
                float knockbackMultiplier = this.inRageMode ? 0.5F : 0.3F; // More knockback in rage mode

                if (knockbackStrength > 0.0) {
                    livingEntity.knockback(knockbackStrength * knockbackMultiplier,
                        Math.sin(this.getYRot() * Math.PI / 180.0F),
                        -Math.cos(this.getYRot() * Math.PI / 180.0F));
                }

                String rageText = this.inRageMode ? " [RAGE MODE]" : "";
                System.out.println("AI Warden: Successfully attacked " + livingEntity.getName().getString() + " for " + finalDamage + " damage (Base: " + baseDamage + ", Multiplier: " + this.damageMultiplier + "x" + rageText + ", NO DARKNESS)");
            }

            return result;
        }
        return false;
    }

    // Override any methods that might trigger sonic boom
    @Override
    public boolean hurt(net.minecraft.world.damagesource.DamageSource damageSource, float damage) {
        boolean result = super.hurt(damageSource, damage);
        // Don't trigger any special behaviors when hurt
        return result;
    }

    // Disable any brain-based behaviors completely
    @Override
    public void updateDynamicGameEventListener(java.util.function.BiConsumer<net.minecraft.world.level.gameevent.DynamicGameEventListener<?>, net.minecraft.server.level.ServerLevel> consumer) {
        // Skip game event listening to prevent sonic boom triggers
    }



    public void performCustomSonicBoom() {
        LivingEntity target = this.getTarget();
        if (target == null) {
            return;
        }

        // Calculate sonic boom damage with command multiplier
        float baseSonicDamage = 10.0F; // Base sonic boom damage
        float reducedDamage = baseSonicDamage * 0.5F; // 50% base reduction
        float finalDamage = reducedDamage * (float)this.damageMultiplier; // Apply command multiplier

        // Rage mode bonus
        if (this.inRageMode) {
            finalDamage *= 1.5F;
        }

        // Deal sonic boom damage
        target.hurt(this.damageSources().sonicBoom(this), finalDamage);

        // Play sonic boom sound
        this.playSound(net.minecraft.sounds.SoundEvents.WARDEN_SONIC_BOOM, 3.0F, 1.0F);

        String rageText = this.inRageMode ? " [RAGE MODE]" : "";
        System.out.println("AI Warden: Sonic boom hit " + target.getName().getString() + " for " + finalDamage + " damage (Base: " + baseSonicDamage + ", Multiplier: " + this.damageMultiplier + "x" + rageText + ")");
    }

    // Override movement speed to ensure our multiplier works
    @Override
    public float getSpeed() {
        return (float)this.getAttributeValue(Attributes.MOVEMENT_SPEED);
    }

    // Block any attempts to apply darkness effects to players
    public void blockDarknessEffects() {
        // This method ensures no darkness effects are applied
        System.out.println("AI Warden: All darkness effects are blocked");
    }



    // Ensure proper initialization
    @Override
    public net.minecraft.world.entity.SpawnGroupData finalizeSpawn(net.minecraft.world.level.ServerLevelAccessor level, net.minecraft.world.DifficultyInstance difficulty, net.minecraft.world.entity.MobSpawnType spawnType, @javax.annotation.Nullable net.minecraft.world.entity.SpawnGroupData spawnGroupData, @javax.annotation.Nullable net.minecraft.nbt.CompoundTag tag) {
        // Call parent for basic initialization
        net.minecraft.world.entity.SpawnGroupData result = super.finalizeSpawn(level, difficulty, spawnType, spawnGroupData, tag);

        // Ensure our goals are properly set up
        System.out.println("AI Warden: Spawned successfully with building AI");
        return result;
    }
}
