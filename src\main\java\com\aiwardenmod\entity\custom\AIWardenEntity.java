package com.aiwardenmod.entity.custom;

import com.aiwardenmod.entity.ai.BuildBlocksToTargetGoal;
import com.aiwardenmod.entity.ai.EnhancedChaseGoal;
import com.aiwardenmod.entity.ai.JumpToTargetGoal;
import net.minecraft.core.BlockPos;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.ai.attributes.AttributeSupplier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.goal.*;
import net.minecraft.world.entity.ai.goal.target.HurtByTargetGoal;
import net.minecraft.world.entity.ai.goal.target.NearestAttackableTargetGoal;
import net.minecraft.world.entity.monster.Monster;
import net.minecraft.world.entity.monster.warden.Warden;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.block.state.BlockState;
import org.jetbrains.annotations.Nullable;

public class AIWardenEntity extends Monster {
    private int blockBuildCooldown = 0;
    private int jumpCooldown = 0;
    private static final int MAX_BUILD_COOLDOWN = 60; // 3 seconds at 20 ticks per second
    private static final int MAX_JUMP_COOLDOWN = 40; // 2 seconds at 20 ticks per second

    public AIWardenEntity(EntityType<? extends Monster> entityType, Level level) {
        super(entityType, level);
        this.xpReward = 50; // Same as regular Warden
    }

    public static AttributeSupplier.Builder createAttributes() {
        return Monster.createMonsterAttributes()
                .add(Attributes.MAX_HEALTH, 500.0D) // Same as Warden
                .add(Attributes.MOVEMENT_SPEED, 0.3D) // Slightly faster than Warden
                .add(Attributes.KNOCKBACK_RESISTANCE, 1.0D) // Immune to knockback
                .add(Attributes.ATTACK_KNOCKBACK, 1.5D)
                .add(Attributes.ATTACK_DAMAGE, 30.0D) // Same as Warden
                .add(Attributes.FOLLOW_RANGE, 24.0D); // Increased follow range
    }

    @Override
    protected void registerGoals() {
        this.goalSelector.addGoal(0, new FloatGoal(this));
        this.goalSelector.addGoal(1, new JumpToTargetGoal(this, 1.0D));
        this.goalSelector.addGoal(2, new BuildBlocksToTargetGoal(this, 1.0D));
        this.goalSelector.addGoal(3, new EnhancedChaseGoal(this, 1.2D, false));
        this.goalSelector.addGoal(4, new MeleeAttackGoal(this, 1.0D, false));
        this.goalSelector.addGoal(5, new WaterAvoidingRandomStrollGoal(this, 0.8D));
        this.goalSelector.addGoal(6, new LookAtPlayerGoal(this, Player.class, 8.0F));
        this.goalSelector.addGoal(7, new RandomLookAroundGoal(this));

        this.targetSelector.addGoal(1, new HurtByTargetGoal(this));
        this.targetSelector.addGoal(2, new NearestAttackableTargetGoal<>(this, Player.class, true));
    }

    @Override
    public void tick() {
        super.tick();
        
        // Decrease cooldowns
        if (blockBuildCooldown > 0) {
            blockBuildCooldown--;
        }
        if (jumpCooldown > 0) {
            jumpCooldown--;
        }
    }

    public boolean canBuildBlocks() {
        return blockBuildCooldown <= 0;
    }

    public boolean canJump() {
        return jumpCooldown <= 0;
    }

    public void setBlockBuildCooldown() {
        this.blockBuildCooldown = MAX_BUILD_COOLDOWN;
    }

    public void setJumpCooldown() {
        this.jumpCooldown = MAX_JUMP_COOLDOWN;
    }

    public boolean tryBuildBlock(BlockPos pos) {
        if (!canBuildBlocks() || !level().isEmptyBlock(pos)) {
            return false;
        }

        // Check if the position is reasonable for building
        if (pos.getY() > this.getY() + 5 || pos.getY() < this.getY() - 2) {
            return false;
        }

        // Place a dirt block
        BlockState blockState = Blocks.DIRT.defaultBlockState();
        level().setBlock(pos, blockState, 3);
        setBlockBuildCooldown();
        
        // Play sound effect
        level().playSound(null, pos, SoundEvents.GRAVEL_PLACE, this.getSoundSource(), 1.0F, 1.0F);
        
        return true;
    }

    public boolean tryJumpToTarget(LivingEntity target) {
        if (!canJump() || target == null) {
            return false;
        }

        double deltaX = target.getX() - this.getX();
        double deltaZ = target.getZ() - this.getZ();
        double deltaY = target.getY() - this.getY();
        
        // Only jump if target is higher and within reasonable range
        if (deltaY > 0.5 && deltaY < 4.0) {
            double distance = Math.sqrt(deltaX * deltaX + deltaZ * deltaZ);
            if (distance < 8.0 && distance > 2.0) {
                // Calculate jump velocity
                double jumpStrength = Math.min(0.8, deltaY * 0.3 + 0.4);
                this.setDeltaMovement(deltaX * 0.1, jumpStrength, deltaZ * 0.1);
                setJumpCooldown();
                
                // Play jump sound
                this.playSound(SoundEvents.RAVAGER_STEP, 1.0F, 0.8F);
                return true;
            }
        }
        
        return false;
    }

    @Override
    protected SoundEvent getAmbientSound() {
        return SoundEvents.WARDEN_AMBIENT;
    }

    @Override
    protected SoundEvent getHurtSound(DamageSource damageSource) {
        return SoundEvents.WARDEN_HURT;
    }

    @Override
    protected SoundEvent getDeathSound() {
        return SoundEvents.WARDEN_DEATH;
    }

    @Override
    protected void playStepSound(BlockPos pos, BlockState blockState) {
        this.playSound(SoundEvents.WARDEN_STEP, 0.15F, 1.0F);
    }

    @Override
    public boolean removeWhenFarAway(double distanceToClosestPlayer) {
        return false; // AI Warden doesn't despawn
    }

    @Override
    public boolean canBeLeashed(Player player) {
        return false; // Cannot be leashed
    }
}
