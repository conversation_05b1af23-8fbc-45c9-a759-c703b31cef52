package com.aiwardenmod.entity.ai;

import com.aiwardenmod.entity.custom.AIWardenEntity;
import net.minecraft.core.BlockPos;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.ai.goal.Goal;
import net.minecraft.world.entity.ai.navigation.PathNavigation;
import net.minecraft.world.level.pathfinder.Path;

import java.util.EnumSet;

public class EnhancedChaseGoal extends Goal {
    private final AIWardenEntity warden;
    private final double speedModifier;
    private final boolean followingEvenIfNotSeen;
    private Path path;
    private double pathedTargetX;
    private double pathedTargetY;
    private double pathedTargetZ;
    private int ticksUntilNextPathRecalculation;
    private int ticksUntilNextAttack;
    private long lastCanUseCheck;
    private int failedPathFindingPenalty = 0;
    private boolean canPenalize = false;

    public EnhancedChaseGoal(AIWardenEntity warden, double speedModifier, boolean followingEvenIfNotSeen) {
        this.warden = warden;
        this.speedModifier = speedModifier;
        this.followingEvenIfNotSeen = followingEvenIfNotSeen;
        this.setFlags(EnumSet.of(Goal.Flag.MOVE, Goal.Flag.LOOK));
    }

    @Override
    public boolean canUse() {
        long gameTime = this.warden.level().getGameTime();
        if (gameTime - this.lastCanUseCheck < 20L) {
            return false;
        } else {
            this.lastCanUseCheck = gameTime;
            LivingEntity target = this.warden.getTarget();
            if (target == null) {
                return false;
            } else if (!target.isAlive()) {
                return false;
            } else {
                if (canPenalize) {
                    if (--this.ticksUntilNextPathRecalculation <= 0) {
                        this.path = this.warden.getNavigation().createPath(target, 0);
                        this.ticksUntilNextPathRecalculation = 4 + this.warden.getRandom().nextInt(7);
                        return this.path != null;
                    } else {
                        return true;
                    }
                }
                this.path = this.warden.getNavigation().createPath(target, 0);
                if (this.path != null) {
                    return true;
                } else {
                    return this.getAttackReachSqr(target) >= this.warden.distanceToSqr(target.getX(), target.getY(), target.getZ());
                }
            }
        }
    }

    @Override
    public boolean canContinueToUse() {
        LivingEntity target = this.warden.getTarget();
        if (target == null) {
            return false;
        } else if (!target.isAlive()) {
            return false;
        } else if (!this.followingEvenIfNotSeen) {
            return !this.warden.getNavigation().isDone();
        } else if (!this.warden.isWithinRestriction(target.blockPosition())) {
            return false;
        } else {
            return !(target instanceof net.minecraft.world.entity.player.Player) || !target.isSpectator() && !((net.minecraft.world.entity.player.Player)target).isCreative();
        }
    }

    @Override
    public void start() {
        this.warden.getNavigation().moveTo(this.path, this.speedModifier);
        this.warden.setAggressive(true);
        this.ticksUntilNextPathRecalculation = 0;
        this.ticksUntilNextAttack = 0;
    }

    @Override
    public void stop() {
        LivingEntity target = this.warden.getTarget();
        if (!net.minecraft.world.entity.EntitySelector.NO_CREATIVE_OR_SPECTATOR.test(target)) {
            this.warden.setTarget(null);
        }

        this.warden.setAggressive(false);
        this.warden.getNavigation().stop();
    }

    @Override
    public boolean requiresUpdateEveryTick() {
        return true;
    }

    @Override
    public void tick() {
        LivingEntity target = this.warden.getTarget();
        if (target != null) {
            this.warden.getLookControl().setLookAt(target, 30.0F, 30.0F);
            double distanceToTarget = this.warden.distanceToSqr(target.getX(), target.getY(), target.getZ());
            this.ticksUntilNextPathRecalculation = Math.max(this.ticksUntilNextPathRecalculation - 1, 0);
            
            // Enhanced pathfinding logic
            if ((this.followingEvenIfNotSeen || this.warden.getSensing().hasLineOfSight(target)) && this.ticksUntilNextPathRecalculation <= 0 && (this.pathedTargetX == 0.0D && this.pathedTargetY == 0.0D && this.pathedTargetZ == 0.0D || target.distanceToSqr(this.pathedTargetX, this.pathedTargetY, this.pathedTargetZ) >= 1.0D || this.warden.getRandom().nextFloat() < 0.05F)) {
                this.pathedTargetX = target.getX();
                this.pathedTargetY = target.getY();
                this.pathedTargetZ = target.getZ();
                this.ticksUntilNextPathRecalculation = 4 + this.warden.getRandom().nextInt(7);
                
                if (distanceToTarget > 1024.0D) {
                    this.ticksUntilNextPathRecalculation += 10;
                } else if (distanceToTarget > 256.0D) {
                    this.ticksUntilNextPathRecalculation += 5;
                }

                if (!this.warden.getNavigation().moveTo(target, this.speedModifier)) {
                    this.ticksUntilNextPathRecalculation += 15;
                }

                this.ticksUntilNextPathRecalculation = this.adjustedTickDelay(this.ticksUntilNextPathRecalculation);
            }

            this.ticksUntilNextAttack = Math.max(this.ticksUntilNextAttack - 1, 0);
            this.checkAndPerformAttack(target, distanceToTarget);
        }
    }

    protected void checkAndPerformAttack(LivingEntity target, double distanceToTarget) {
        double attackReach = this.getAttackReachSqr(target);
        if (distanceToTarget <= attackReach && this.ticksUntilNextAttack <= 0) {
            this.resetAttackCooldown();
            this.warden.swing(this.warden.getUsedItemHand());
            this.warden.doHurtTarget(target);
        }
    }

    protected void resetAttackCooldown() {
        this.ticksUntilNextAttack = this.adjustedTickDelay(20);
    }

    protected boolean isTimeToAttack() {
        return this.ticksUntilNextAttack <= 0;
    }

    protected int getTicksUntilNextAttack() {
        return this.ticksUntilNextAttack;
    }

    protected double getAttackReachSqr(LivingEntity target) {
        return this.warden.getBbWidth() * 2.0F * this.warden.getBbWidth() * 2.0F + target.getBbWidth();
    }
}
