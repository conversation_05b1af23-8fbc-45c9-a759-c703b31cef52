package com.aiwardenmod.item;

import com.aiwardenmod.AIWardenMod;
import net.minecraft.world.item.Item;
import net.minecraftforge.eventbus.api.IEventBus;
import net.minecraftforge.registries.DeferredRegister;
import net.minecraftforge.registries.ForgeRegistries;
import net.minecraftforge.registries.RegistryObject;

public class ModItems {
    public static final DeferredRegister<Item> ITEMS =
            DeferredRegister.create(ForgeRegistries.ITEMS, AIWardenMod.MODID);

    public static final RegistryObject<Item> AI_WARDEN_SPAWN_EGG = ITEMS.register("ai_warden_spawn_egg",
            AIWardenSpawnEggItem::new);

    public static void register(IEventBus eventBus) {
        ITEMS.register(eventBus);
    }
}
