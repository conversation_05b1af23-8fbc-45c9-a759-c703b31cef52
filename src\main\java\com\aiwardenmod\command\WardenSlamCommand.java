package com.aiwardenmod.command;

import com.aiwardenmod.entity.custom.AIWardenEntity;
import com.mojang.brigadier.CommandDispatcher;
import com.mojang.brigadier.arguments.DoubleArgumentType;
import com.mojang.brigadier.context.CommandContext;
import net.minecraft.commands.CommandSourceStack;
import net.minecraft.commands.Commands;
import net.minecraft.network.chat.Component;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.phys.AABB;

import java.util.List;

public class WardenSlamCommand {
    
    public static void register(CommandDispatcher<CommandSourceStack> dispatcher) {
        dispatcher.register(Commands.literal("wardenslam")
            .requires(source -> source.hasPermission(2)) // Requires OP level 2
            .then(Commands.argument("radius", DoubleArgumentType.doubleArg(1.0, 100.0))
                .executes(context -> executeWithRadius(context, DoubleArgumentType.getDouble(context, "radius"))))
            .executes(context -> executeWithRadius(context, 50.0)) // Default radius of 50 blocks
        );
    }
    
    private static int executeWithRadius(CommandContext<CommandSourceStack> context, double radius) {
        CommandSourceStack source = context.getSource();
        
        if (!(source.getLevel() instanceof ServerLevel serverLevel)) {
            source.sendFailure(Component.literal("This command can only be used in a server world!"));
            return 0;
        }
        
        if (!(source.getEntity() instanceof ServerPlayer player)) {
            source.sendFailure(Component.literal("This command can only be used by a player!"));
            return 0;
        }
        
        // Find all AI Wardens within the specified radius
        AABB searchArea = new AABB(
            player.getX() - radius, player.getY() - radius, player.getZ() - radius,
            player.getX() + radius, player.getY() + radius, player.getZ() + radius
        );
        
        List<AIWardenEntity> wardens = serverLevel.getEntitiesOfClass(AIWardenEntity.class, searchArea);
        
        if (wardens.isEmpty()) {
            source.sendFailure(Component.literal("No AI Wardens found within " + radius + " blocks!"));
            return 0;
        }
        
        int triggeredCount = 0;
        for (AIWardenEntity warden : wardens) {
            if (warden.isAlive()) {
                warden.triggerAerialSlam();
                triggeredCount++;
            }
        }
        
        if (triggeredCount > 0) {
            source.sendSuccess(() -> Component.literal("Triggered aerial slam for " + triggeredCount + " AI Warden(s)!"), true);
            return triggeredCount;
        } else {
            source.sendFailure(Component.literal("No living AI Wardens found to trigger!"));
            return 0;
        }
    }
}
