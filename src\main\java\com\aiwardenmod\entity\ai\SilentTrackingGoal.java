package com.aiwardenmod.entity.ai;

import com.aiwardenmod.entity.custom.AIWardenEntity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.ai.goal.Goal;
import net.minecraft.world.entity.player.Player;

import java.util.EnumSet;

public class SilentTrackingGoal extends Goal {
    private final AIWardenEntity warden;
    private LivingEntity target;
    private int trackingCooldown = 0;
    private int lastKnownX = 0;
    private int lastKnownY = 0;
    private int lastKnownZ = 0;
    private boolean hasLastKnownPosition = false;
    private static final double SLOW_SPEED = 0.4D; // Very slow movement speed
    private static final int TRACKING_INTERVAL = 40; // 2 seconds between tracking updates
    private static final double DETECTION_RADIUS = 80.0; // Large detection radius

    public SilentTrackingGoal(AIWardenEntity warden) {
        this.warden = warden;
        this.setFlags(EnumSet.of(Goal.Flag.MOVE, Goal.Flag.LOOK));
    }

    @Override
    public boolean canUse() {
        // Always try to track players, even when silent
        this.target = this.warden.getTarget();
        
        if (this.target == null || !this.target.isAlive()) {
            // Find the nearest player even if no target is set
            this.target = findNearestPlayer();
            if (this.target != null) {
                this.warden.setTarget(this.target);
            }
        }

        // Only activate if we have a target and they're not too close (to avoid interfering with combat)
        if (this.target != null) {
            double distance = this.warden.distanceTo(this.target);
            boolean shouldTrack = distance > 5.0 && distance < DETECTION_RADIUS;
            
            if (shouldTrack) {
                System.out.println("AI Warden: Silent tracking activated for " + this.target.getName().getString() + " at distance " + String.format("%.1f", distance));
            }
            
            return shouldTrack;
        }

        return false;
    }

    @Override
    public boolean canContinueToUse() {
        if (this.target == null || !this.target.isAlive()) {
            return false;
        }

        double distance = this.warden.distanceTo(this.target);
        
        // Stop tracking if too close (let combat goals take over) or too far
        if (distance <= 5.0) {
            System.out.println("AI Warden: Stopping silent tracking - target too close for combat");
            return false;
        }
        
        if (distance > DETECTION_RADIUS) {
            System.out.println("AI Warden: Stopping silent tracking - target too far away");
            return false;
        }

        return true;
    }

    @Override
    public void start() {
        this.trackingCooldown = 0;
        System.out.println("AI Warden: Started silent tracking mode - moving slowly toward target");
    }

    @Override
    public void stop() {
        this.target = null;
        this.hasLastKnownPosition = false;
        System.out.println("AI Warden: Stopped silent tracking mode");
    }

    @Override
    public void tick() {
        if (this.target == null) {
            return;
        }

        if (this.trackingCooldown > 0) {
            this.trackingCooldown--;
        }

        // Update tracking every 2 seconds for slow, methodical pursuit
        if (this.trackingCooldown <= 0) {
            updateTracking();
            this.trackingCooldown = TRACKING_INTERVAL;
        }

        // Always look toward target or last known position
        if (this.target.isAlive()) {
            this.warden.getLookControl().setLookAt(this.target, 10.0F, 10.0F);
        } else if (this.hasLastKnownPosition) {
            this.warden.getLookControl().setLookAt(this.lastKnownX, this.lastKnownY, this.lastKnownZ, 10.0F, 10.0F);
        }

        // Move slowly toward target or last known position
        moveTowardTarget();
    }

    private void updateTracking() {
        if (this.target == null) {
            return;
        }

        // Update last known position
        this.lastKnownX = (int) this.target.getX();
        this.lastKnownY = (int) this.target.getY();
        this.lastKnownZ = (int) this.target.getZ();
        this.hasLastKnownPosition = true;

        double distance = this.warden.distanceTo(this.target);
        System.out.println("AI Warden: Tracking update - target at (" + this.lastKnownX + ", " + this.lastKnownY + ", " + this.lastKnownZ + ") distance: " + String.format("%.1f", distance));
    }

    private void moveTowardTarget() {
        if (this.target != null && this.target.isAlive()) {
            // Move directly toward living target
            boolean pathSuccess = this.warden.getNavigation().moveTo(this.target, SLOW_SPEED);
            if (!pathSuccess) {
                // If direct pathing fails, try to get closer
                this.warden.getNavigation().moveTo(this.target.getX(), this.target.getY(), this.target.getZ(), SLOW_SPEED);
            }
        } else if (this.hasLastKnownPosition) {
            // Move toward last known position
            this.warden.getNavigation().moveTo(this.lastKnownX + 0.5, this.lastKnownY, this.lastKnownZ + 0.5, SLOW_SPEED);
            System.out.println("AI Warden: Moving to last known position (" + this.lastKnownX + ", " + this.lastKnownY + ", " + this.lastKnownZ + ")");
        }
    }

    private Player findNearestPlayer() {
        // Enhanced player detection for silent tracking
        java.util.List<Player> players = this.warden.level().getEntitiesOfClass(Player.class, 
            this.warden.getBoundingBox().inflate(DETECTION_RADIUS));
        
        Player nearestPlayer = null;
        double nearestDistance = Double.MAX_VALUE;
        
        for (Player player : players) {
            if (player.isSpectator() || player.isCreative()) {
                continue;
            }
            
            double distance = this.warden.distanceTo(player);
            if (distance < nearestDistance && distance > 5.0) { // Don't track if too close
                nearestDistance = distance;
                nearestPlayer = player;
            }
        }
        
        if (nearestPlayer != null) {
            System.out.println("AI Warden: Silent tracking found player " + nearestPlayer.getName().getString() + " at distance " + String.format("%.1f", nearestDistance));
        }
        
        return nearestPlayer;
    }

    // Method to check if warden is currently in silent tracking mode
    public boolean isTracking() {
        return this.target != null;
    }

    // Method to get current tracking target
    public LivingEntity getTrackingTarget() {
        return this.target;
    }

    // Method to force update tracking (can be called externally)
    public void forceTrackingUpdate() {
        this.trackingCooldown = 0;
        updateTracking();
    }
}
