package com.aiwardenmod.entity.ai;

import com.aiwardenmod.entity.custom.AIWardenEntity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.ai.goal.Goal;
import net.minecraft.world.entity.player.Player;

import java.util.EnumSet;

public class AggressiveChaseGoal extends Goal {
    private final AIWardenEntity warden;
    private LivingEntity target;
    private int chaseTimer = 0;
    private int jumpCooldown = 0;

    public AggressiveChaseGoal(AIWardenEntity warden) {
        this.warden = warden;
        this.setFlags(EnumSet.of(Goal.Flag.MOVE, Goal.Flag.LOOK));
    }

    @Override
    public boolean canUse() {
        this.target = this.warden.getTarget();
        if (this.target == null || !this.target.isAlive()) {
            return false;
        }

        // Only activate when at same level as target and within chase range
        double heightDifference = Math.abs(this.target.getY() - this.warden.getY());
        double distance = this.warden.distanceTo(this.target);

        // Activate when at same level (within 2 blocks) and close enough to chase
        boolean shouldChase = heightDifference <= 2.0 && distance <= 20.0 && distance > 3.0;

        if (shouldChase) {
            System.out.println("AI Warden: AGGRESSIVE CHASE activated! Height diff: " + String.format("%.1f", heightDifference) + ", Distance: " + String.format("%.1f", distance));
        }

        return shouldChase;
    }

    @Override
    public boolean canContinueToUse() {
        if (this.target == null || !this.target.isAlive()) {
            return false;
        }

        double heightDifference = Math.abs(this.target.getY() - this.warden.getY());
        double distance = this.warden.distanceTo(this.target);

        // Continue chasing as long as we're at same level and not too close
        return heightDifference <= 3.0 && distance <= 25.0 && distance > 2.0;
    }

    @Override
    public void start() {
        this.chaseTimer = 0;
        this.jumpCooldown = 0;
        System.out.println("AI Warden: Starting AGGRESSIVE CHASE mode!");
    }

    @Override
    public void stop() {
        this.warden.getNavigation().stop();
        this.chaseTimer = 0;
        System.out.println("AI Warden: Stopping aggressive chase");
    }

    @Override
    public void tick() {
        if (this.target == null) {
            return;
        }

        this.chaseTimer++;
        if (this.jumpCooldown > 0) {
            this.jumpCooldown--;
        }

        // Look at target aggressively
        this.warden.getLookControl().setLookAt(this.target, 30.0F, 30.0F);

        double distance = this.warden.distanceTo(this.target);

        // CHECK IF BRIDGE BUILDING IS NEEDED
        if (this.chaseTimer % 10 == 0) { // Check every 10 ticks
            if (needsBridgeToReachTarget()) {
                System.out.println("AI Warden: Need bridge to reach target! Switching to bridge building mode.");
                // Force building goal to activate by making it think target is far
                return; // Let building goal handle bridge construction
            }
        }

        // AGGRESSIVE NAVIGATION - fast movement toward target
        if (this.chaseTimer % 5 == 0) { // Update path every 5 ticks
            boolean pathSuccess = this.warden.getNavigation().moveTo(this.target, 1.5); // Fast speed

            if (!pathSuccess) {
                // Can't path to target - might need bridge
                System.out.println("AI Warden: Can't path to target - may need bridge building!");
            } else {
                System.out.println("AI Warden: Aggressively chasing target at distance " + String.format("%.1f", distance));
            }
        }

        // JUMP ATTACKS when close
        if (distance <= 8.0 && distance > 3.0 && this.jumpCooldown <= 0 && this.warden.onGround()) {
            performJumpAttack();
            this.jumpCooldown = 40; // 2 second cooldown
        }

        // SPEED BOOST when target is moving away
        if (this.target instanceof Player player) {
            double targetSpeed = player.getDeltaMovement().horizontalDistanceSqr();
            if (targetSpeed > 0.01 && distance > 6.0) {
                // Give warden extra speed to catch up
                double deltaX = this.target.getX() - this.warden.getX();
                double deltaZ = this.target.getZ() - this.warden.getZ();
                double horizontalDistance = Math.sqrt(deltaX * deltaX + deltaZ * deltaZ);

                if (horizontalDistance > 0) {
                    this.warden.setDeltaMovement(
                        this.warden.getDeltaMovement().x + (deltaX / horizontalDistance) * 0.1,
                        this.warden.getDeltaMovement().y,
                        this.warden.getDeltaMovement().z + (deltaZ / horizontalDistance) * 0.1
                    );
                    System.out.println("AI Warden: Speed boost to catch fleeing target!");
                }
            }
        }
    }

    private void performJumpAttack() {
        if (this.target == null || !this.warden.onGround()) {
            return;
        }

        // Calculate jump direction toward target
        double deltaX = this.target.getX() - this.warden.getX();
        double deltaZ = this.target.getZ() - this.warden.getZ();
        double distance = Math.sqrt(deltaX * deltaX + deltaZ * deltaZ);

        if (distance > 0) {
            // Powerful jump toward target
            double jumpStrength = 0.6;
            double horizontalSpeed = Math.min(0.8, distance * 0.2); // Scale with distance

            this.warden.setDeltaMovement(
                (deltaX / distance) * horizontalSpeed,
                jumpStrength,
                (deltaZ / distance) * horizontalSpeed
            );

            // Play aggressive sound
            this.warden.playSound(net.minecraft.sounds.SoundEvents.WARDEN_ROAR, 1.5F, 0.8F);
            System.out.println("AI Warden: JUMP ATTACK toward target at distance " + String.format("%.1f", distance) + "!");
        }
    }

    private boolean needsBridgeToReachTarget() {
        if (this.target == null) {
            return false;
        }

        // Check if there's a gap or obstacle between warden and target
        double deltaX = this.target.getX() - this.warden.getX();
        double deltaZ = this.target.getZ() - this.warden.getZ();
        double horizontalDistance = Math.sqrt(deltaX * deltaX + deltaZ * deltaZ);

        if (horizontalDistance < 3.0) {
            return false; // Too close to need bridge
        }

        // Check path in steps toward target
        int steps = (int) Math.min(horizontalDistance, 10); // Check up to 10 blocks
        for (int i = 1; i <= steps; i++) {
            double stepX = this.warden.getX() + (deltaX / steps) * i;
            double stepZ = this.warden.getZ() + (deltaZ / steps) * i;
            double stepY = this.warden.getY();

            // Check if there's a gap (no solid block below)
            net.minecraft.core.BlockPos checkPos = new net.minecraft.core.BlockPos((int) stepX, (int) stepY - 1, (int) stepZ);
            if (this.warden.level().isEmptyBlock(checkPos)) {
                // Check if it's a significant gap (more than 1 block down)
                net.minecraft.core.BlockPos belowPos = checkPos.below();
                if (this.warden.level().isEmptyBlock(belowPos)) {
                    System.out.println("AI Warden: Detected gap at " + checkPos + " - bridge needed!");
                    return true;
                }
            }

            // Check if there's a wall blocking the path
            net.minecraft.core.BlockPos wallPos = new net.minecraft.core.BlockPos((int) stepX, (int) stepY, (int) stepZ);
            if (!this.warden.level().isEmptyBlock(wallPos)) {
                net.minecraft.core.BlockPos aboveWall = wallPos.above();
                if (!this.warden.level().isEmptyBlock(aboveWall)) {
                    System.out.println("AI Warden: Detected wall at " + wallPos + " - bridge needed!");
                    return true;
                }
            }
        }

        return false;
    }
}
