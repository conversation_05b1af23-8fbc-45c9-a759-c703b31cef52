package com.aiwardenmod.entity.ai;

import com.aiwardenmod.entity.custom.AIWardenEntity;
import net.minecraft.core.BlockPos;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.ai.goal.Goal;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import net.minecraft.world.phys.Vec3;

import java.util.EnumSet;
import java.util.List;

public class AerialSlamGoal extends Goal {
    private final AIWardenEntity warden;
    private LivingEntity target;
    private int slamCooldown = 0;
    private boolean isPerformingSlam = false;
    private int slamPhase = 0; // 0 = not active, 1 = flying up, 2 = targeting, 3 = diving down
    private int phaseTimer = 0;
    private Vec3 targetPosition = null;
    private static final int SLAM_COOLDOWN = 400; // 20 seconds between slams
    private static final int FLY_UP_DURATION = 40; // 2 seconds to fly up
    private static final int TARGET_DURATION = 60; // 3 seconds to target
    private static final int DIVE_DURATION = 30; // 1.5 seconds to dive down

    public AerialSlamGoal(AIWardenEntity warden) {
        this.warden = warden;
        this.setFlags(EnumSet.of(Goal.Flag.MOVE, Goal.Flag.LOOK, Goal.Flag.JUMP));
    }

    @Override
    public boolean canUse() {
        this.target = this.warden.getTarget();
        if (this.target == null || !this.target.isAlive()) {
            return false;
        }

        // Check if command triggered first (highest priority)
        if (this.warden.isCommandTriggeredSlam()) {
            System.out.println("AI Warden: Aerial slam activated by COMMAND!");
            return this.slamCooldown <= 0 && this.warden.onGround() && !this.isPerformingSlam;
        }

        // Natural activation conditions (aggressive)
        double distance = this.warden.distanceTo(this.target);
        double heightDifference = this.target.getY() - this.warden.getY();

        // Activate if target is higher and within range, OR if target is far away
        boolean shouldSlam = (heightDifference > 4.0 && distance < 40.0) || // Target much higher
                           (distance > 20.0 && distance < 60.0) || // Target far away
                           (heightDifference > 2.0 && distance > 12.0); // Target higher and distant

        if (shouldSlam && this.slamCooldown <= 0 && this.warden.onGround() && !this.isPerformingSlam) {
            System.out.println("AI Warden: Aerial slam activated NATURALLY! Height diff: " + String.format("%.1f", heightDifference) + ", Distance: " + String.format("%.1f", distance));
            return true;
        }

        return false;
    }

    @Override
    public boolean canContinueToUse() {
        return this.isPerformingSlam && this.target != null && this.target.isAlive();
    }

    @Override
    public void start() {
        this.isPerformingSlam = true;
        this.slamPhase = 1; // Start flying up
        this.phaseTimer = 0;
        this.warden.setNoGravity(true);

        // Reset command trigger immediately when starting
        if (this.warden.isCommandTriggeredSlam()) {
            this.warden.resetCommandTriggeredSlam();
            System.out.println("AI Warden: Starting COMMAND-TRIGGERED aerial slam attack!");
        } else {
            System.out.println("AI Warden: Starting NATURAL aerial slam attack!");
        }

        // Play dramatic start sound
        this.warden.playSound(SoundEvents.ENDER_DRAGON_GROWL, 2.0F, 0.5F);
    }

    @Override
    public void stop() {
        this.isPerformingSlam = false;
        this.slamPhase = 0;
        this.phaseTimer = 0;
        this.targetPosition = null;
        this.warden.setNoGravity(false);
        this.slamCooldown = SLAM_COOLDOWN;
        this.warden.resetCommandTriggeredSlam(); // Reset the command trigger

        System.out.println("AI Warden: Aerial slam attack completed");
    }

    @Override
    public void tick() {
        if (!this.isPerformingSlam || this.target == null) {
            return;
        }

        if (this.slamCooldown > 0) {
            this.slamCooldown--;
        }

        this.phaseTimer++;

        switch (this.slamPhase) {
            case 1: // Flying up phase
                performFlyUpPhase();
                break;
            case 2: // Targeting phase
                performTargetingPhase();
                break;
            case 3: // Diving down phase
                performDivingPhase();
                break;
        }
    }

    private void performFlyUpPhase() {
        // Fly straight up rapidly
        this.warden.setDeltaMovement(0, 1.2, 0); // Strong upward velocity

        // Create ascending particles
        if (this.warden.level() instanceof ServerLevel serverLevel) {
            serverLevel.sendParticles(
                ParticleTypes.SOUL_FIRE_FLAME,
                this.warden.getX(), this.warden.getY(), this.warden.getZ(),
                5, 0.5, 0.5, 0.5, 0.1
            );
        }

        // Play flying sound occasionally
        if (this.phaseTimer % 10 == 0) {
            this.warden.playSound(SoundEvents.PHANTOM_FLAP, 1.0F, 0.8F);
        }

        // Transition to targeting phase
        if (this.phaseTimer >= FLY_UP_DURATION) {
            this.slamPhase = 2;
            this.phaseTimer = 0;
            System.out.println("AI Warden: Reached peak height, entering targeting phase");
        }
    }

    private void performTargetingPhase() {
        // Hover in place and stare at target menacingly
        this.warden.setDeltaMovement(0, 0, 0); // Stop all movement

        // Intensely stare at target
        this.warden.getLookControl().setLookAt(this.target, 30.0F, 30.0F);

        // Update target position (predict where player will be)
        Vec3 targetVelocity = this.target.getDeltaMovement();
        this.targetPosition = this.target.position().add(targetVelocity.scale(1.5)); // Predict 1.5 seconds ahead

        // Create ominous targeting particles
        if (this.warden.level() instanceof ServerLevel serverLevel) {
            // Particles around warden
            serverLevel.sendParticles(
                ParticleTypes.SCULK_SOUL,
                this.warden.getX(), this.warden.getY(), this.warden.getZ(),
                8, 1.0, 1.0, 1.0, 0.05
            );

            // Targeting beam toward player
            Vec3 wardenPos = this.warden.position();
            Vec3 targetPos = this.target.position();
            Vec3 direction = targetPos.subtract(wardenPos).normalize();

            for (int i = 1; i <= 10; i++) {
                Vec3 particlePos = wardenPos.add(direction.scale(i * 2));
                serverLevel.sendParticles(
                    ParticleTypes.CRIMSON_SPORE,
                    particlePos.x, particlePos.y, particlePos.z,
                    1, 0.1, 0.1, 0.1, 0.01
                );
            }
        }

        // Play targeting sound
        if (this.phaseTimer % 20 == 0) {
            this.warden.playSound(SoundEvents.WARDEN_HEARTBEAT, 1.5F, 0.6F);
        }

        // Transition to diving phase
        if (this.phaseTimer >= TARGET_DURATION) {
            this.slamPhase = 3;
            this.phaseTimer = 0;
            System.out.println("AI Warden: Target locked! Beginning dive attack!");

            // Play dive start sound
            this.warden.playSound(SoundEvents.WARDEN_ROAR, 2.0F, 0.7F);
        }
    }

    private void performDivingPhase() {
        if (this.targetPosition == null) {
            this.targetPosition = this.target.position();
        }

        // Calculate dive direction
        Vec3 wardenPos = this.warden.position();
        Vec3 direction = this.targetPosition.subtract(wardenPos).normalize();

        // Powerful dive toward target
        double diveSpeed = 2.0;
        this.warden.setDeltaMovement(
            direction.x * diveSpeed,
            direction.y * diveSpeed - 0.5, // Extra downward force
            direction.z * diveSpeed
        );

        // Create dive trail particles
        if (this.warden.level() instanceof ServerLevel serverLevel) {
            serverLevel.sendParticles(
                ParticleTypes.SONIC_BOOM,
                this.warden.getX(), this.warden.getY(), this.warden.getZ(),
                3, 0.2, 0.2, 0.2, 0.1
            );
        }

        // Enhanced landing detection with better range
        boolean shouldLand = this.warden.onGround() ||
                           this.warden.getY() <= this.targetPosition.y + 3 || // More lenient height check
                           this.warden.distanceToSqr(this.targetPosition.x, this.targetPosition.y, this.targetPosition.z) <= 4.0; // Close to target

        if (shouldLand) {
            // Force teleport to target if still in air to ensure landing
            if (!this.warden.onGround() && this.warden.getY() > this.targetPosition.y + 1) {
                this.warden.teleportTo(this.targetPosition.x, this.targetPosition.y + 1, this.targetPosition.z);
                System.out.println("AI Warden: Force landing at target position");
            }
            performSlamImpact();
        }

        // Safety timeout with force landing
        if (this.phaseTimer >= DIVE_DURATION) {
            this.warden.teleportTo(this.targetPosition.x, this.targetPosition.y + 1, this.targetPosition.z);
            System.out.println("AI Warden: Timeout - force landing at target");
            performSlamImpact();
        }
    }

    private void performSlamImpact() {
        System.out.println("AI Warden: SLAM IMPACT!");

        // Create massive explosion effect
        Level level = this.warden.level();
        BlockPos impactPos = this.warden.blockPosition();

        // Explosion particles and effects
        if (level instanceof ServerLevel serverLevel) {
            // Main explosion
            serverLevel.sendParticles(
                ParticleTypes.EXPLOSION,
                this.warden.getX(), this.warden.getY(), this.warden.getZ(),
                20, 2.0, 1.0, 2.0, 0.1
            );

            // Shockwave particles
            for (int i = 0; i < 360; i += 15) {
                double angle = Math.toRadians(i);
                double x = this.warden.getX() + Math.cos(angle) * 3;
                double z = this.warden.getZ() + Math.sin(angle) * 3;

                serverLevel.sendParticles(
                    ParticleTypes.SCULK_CHARGE_POP,
                    x, this.warden.getY(), z,
                    2, 0.1, 0.1, 0.1, 0.05
                );
            }
        }

        // Damage and launch all nearby players with COMMAND-CONTROLLED DAMAGE
        List<Player> nearbyPlayers = level.getEntitiesOfClass(Player.class,
            this.warden.getBoundingBox().inflate(6.0));

        for (Player player : nearbyPlayers) {
            if (player.isSpectator() || player.isCreative()) {
                continue;
            }

            // Calculate distance for damage scaling with COMMAND-CONTROLLED DAMAGE
            double distance = this.warden.distanceTo(player);
            float originalDamage = (float) Math.max(10.0, 25.0 - (distance * 2.0)); // Original: 25 damage at center, 10 minimum
            float reducedDamage = originalDamage * 0.5F; // 50% base reduction
            float finalDamage = reducedDamage * (float)this.warden.getDamageMultiplier(); // Apply command multiplier

            // Rage mode bonus
            if (this.warden.isInRageMode()) {
                finalDamage *= 1.5F;
            }

            // Deal command-controlled damage
            player.hurt(this.warden.damageSources().mobAttack(this.warden), finalDamage);

            // Knockback affected by rage mode
            double deltaX = player.getX() - this.warden.getX();
            double deltaZ = player.getZ() - this.warden.getZ();
            double horizontalDistance = Math.sqrt(deltaX * deltaX + deltaZ * deltaZ);

            if (horizontalDistance > 0) {
                // Launch strength affected by rage mode
                double baseLaunchStrength = Math.max(1.0, 2.0 - (distance * 0.2));
                double launchStrength = this.warden.isInRageMode() ? baseLaunchStrength * 1.3 : baseLaunchStrength;
                float horizontalMultiplier = this.warden.isInRageMode() ? 0.7F : 0.5F;
                float verticalMultiplier = this.warden.isInRageMode() ? 1.3F : 1.0F;

                player.setDeltaMovement(
                    (deltaX / horizontalDistance) * launchStrength * horizontalMultiplier,
                    launchStrength * verticalMultiplier,
                    (deltaZ / horizontalDistance) * launchStrength * horizontalMultiplier
                );

                String rageText = this.warden.isInRageMode() ? " [RAGE MODE]" : "";
                System.out.println("AI Warden: Launched " + player.getName().getString() + " into the air (DAMAGE: " + finalDamage + " from base " + originalDamage + ", Multiplier: " + this.warden.getDamageMultiplier() + "x" + rageText + ")!");
            }
        }

        // Play massive impact sound
        this.warden.playSound(SoundEvents.GENERIC_EXPLODE, 3.0F, 0.5F);
        this.warden.playSound(SoundEvents.WARDEN_ATTACK_IMPACT, 2.0F, 0.6F);

        // End the slam attack
        this.stop();
    }
}
