package com.aiwardenmod.entity.client;

import com.aiwardenmod.entity.custom.AIWardenEntity;
import net.minecraft.client.renderer.entity.EntityRendererProvider;
import net.minecraft.client.renderer.entity.MobRenderer;
import net.minecraft.client.model.WardenModel;
import net.minecraft.client.model.geom.ModelLayers;
import net.minecraft.resources.ResourceLocation;

public class <PERSON><PERSON><PERSON>en<PERSON>enderer extends <PERSON>b<PERSON><PERSON>er<AIWardenEntity, WardenModel<AIWardenEntity>> {
    // Use the default Warden texture from Minecraft
    private static final ResourceLocation TEXTURE = new ResourceLocation("minecraft", "textures/entity/warden/warden.png");

    public AIWardenRenderer(EntityRendererProvider.Context context) {
        super(context, new WardenModel<>(context.bakeLayer(ModelLayers.WARDEN)), 0.9F);
    }

    @Override
    public ResourceLocation getTextureLocation(AIWardenEntity entity) {
        return TEXTURE;
    }
}
