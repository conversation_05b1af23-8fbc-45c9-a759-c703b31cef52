package com.aiwardenmod.entity.ai;

import com.aiwardenmod.entity.custom.AIWardenEntity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.ai.goal.Goal;
import net.minecraft.world.entity.player.Player;

import java.util.EnumSet;

public class PersistentChaseGoal extends Goal {
    private final AIWardenEntity warden;
    private LivingEntity target;
    private int chaseTimer = 0;
    private int lostTargetTimer = 0;
    private double lastTargetX, lastTargetY, lastTargetZ;

    public PersistentChaseGoal(AIWardenEntity warden) {
        this.warden = warden;
        this.setFlags(EnumSet.of(Goal.Flag.MOVE, Goal.Flag.LOOK));
    }

    @Override
    public boolean canUse() {
        this.target = this.warden.getTarget();
        if (this.target == null || !this.target.isAlive()) {
            return false;
        }

        // Always chase when we have a target - like default Warden behavior
        // This goal has lower priority so building and aggressive chase take precedence
        double distance = this.warden.distanceTo(this.target);
        
        // Activate for persistent long-range chasing
        boolean shouldChase = distance > 8.0 && distance < 200.0; // Long range persistent chase

        if (shouldChase) {
            System.out.println("AI Warden: PERSISTENT CHASE activated for long-range pursuit! Distance: " + String.format("%.1f", distance));
        }

        return shouldChase;
    }

    @Override
    public boolean canContinueToUse() {
        if (this.target == null || !this.target.isAlive()) {
            return false;
        }

        double distance = this.warden.distanceTo(this.target);
        
        // Continue chasing until very close (let other goals handle close combat)
        return distance > 6.0 && distance < 250.0;
    }

    @Override
    public void start() {
        this.chaseTimer = 0;
        this.lostTargetTimer = 0;
        System.out.println("AI Warden: Starting PERSISTENT CHASE - relentless pursuit mode!");
    }

    @Override
    public void stop() {
        this.warden.getNavigation().stop();
        this.chaseTimer = 0;
        System.out.println("AI Warden: Stopping persistent chase");
    }

    @Override
    public void tick() {
        if (this.target == null) {
            return;
        }

        this.chaseTimer++;

        // Always look at target
        this.warden.getLookControl().setLookAt(this.target, 30.0F, 30.0F);

        double distance = this.warden.distanceTo(this.target);

        // Update last known position
        this.lastTargetX = this.target.getX();
        this.lastTargetY = this.target.getY();
        this.lastTargetZ = this.target.getZ();

        // PERSISTENT NAVIGATION - like default Warden
        if (this.chaseTimer % 10 == 0) { // Update path every 10 ticks for persistence
            boolean pathSuccess = this.warden.getNavigation().moveTo(this.target, 1.0); // Normal speed for persistence
            
            if (pathSuccess) {
                this.lostTargetTimer = 0;
                System.out.println("AI Warden: Persistently chasing target at distance " + String.format("%.1f", distance));
            } else {
                this.lostTargetTimer++;
                System.out.println("AI Warden: Lost path to target, investigating...");
                
                // Try to path to last known position
                this.warden.getNavigation().moveTo(this.lastTargetX, this.lastTargetY, this.lastTargetZ, 1.0);
            }
        }

        // RELENTLESS BEHAVIOR - never give up like default Warden
        if (this.target instanceof Player player) {
            // Detect if player is moving and adjust behavior
            double playerSpeed = player.getDeltaMovement().horizontalDistanceSqr();
            
            if (playerSpeed > 0.01) {
                // Player is moving - increase chase frequency
                if (this.chaseTimer % 5 == 0) {
                    this.warden.getNavigation().moveTo(this.target, 1.2); // Faster when player moves
                    System.out.println("AI Warden: Player moving - increasing chase intensity!");
                }
            }
            
            // SOUND TRACKING - respond to player actions
            if (player.isSprinting() || !player.onGround()) {
                // Player making noise - immediate response
                this.warden.getNavigation().moveTo(this.target, 1.3);
                System.out.println("AI Warden: Detected player noise - immediate pursuit!");
            }
        }

        // INVESTIGATION BEHAVIOR - search last known area
        if (this.lostTargetTimer > 20) { // Lost for 1 second
            // Search around last known position
            double searchRadius = 5.0;
            double searchX = this.lastTargetX + (this.warden.getRandom().nextDouble() - 0.5) * searchRadius * 2;
            double searchZ = this.lastTargetZ + (this.warden.getRandom().nextDouble() - 0.5) * searchRadius * 2;
            
            this.warden.getNavigation().moveTo(searchX, this.lastTargetY, searchZ, 0.8);
            System.out.println("AI Warden: Investigating area around last known position...");
            
            this.lostTargetTimer = 0; // Reset to prevent spam
        }

        // LONG-RANGE DETECTION - expand search when target lost
        if (this.chaseTimer % 40 == 0) { // Every 2 seconds
            // Try to reacquire target if lost
            for (Player nearbyPlayer : this.warden.level().getEntitiesOfClass(Player.class, 
                    this.warden.getBoundingBox().inflate(100.0))) {
                
                if (nearbyPlayer.isSpectator() || nearbyPlayer.isCreative()) {
                    continue;
                }
                
                // Reacquire any nearby player
                this.warden.setTarget(nearbyPlayer);
                this.target = nearbyPlayer;
                System.out.println("AI Warden: Reacquired target during persistent chase!");
                break;
            }
        }
    }
}
