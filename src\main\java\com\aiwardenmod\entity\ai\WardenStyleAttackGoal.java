package com.aiwardenmod.entity.ai;

import com.aiwardenmod.entity.custom.AIWardenEntity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.ai.goal.Goal;
import net.minecraft.world.entity.player.Player;
import net.minecraft.sounds.SoundEvents;

import java.util.EnumSet;

public class WardenStyleAttackGoal extends Goal {
    private final AIWardenEntity warden;
    private LivingEntity target;
    private int attackTimer = 0;
    private int sonicBoomCooldown = 0;
    private int emergingTimer = 0;
    private boolean isEmerging = false;

    // Attack patterns like original Warden
    private static final int MELEE_ATTACK_INTERVAL = 20; // 1 second between melee attacks
    private static final int SONIC_BOOM_INTERVAL = 80; // 4 seconds between sonic booms
    private static final int EMERGING_DURATION = 40; // 2 seconds emerging animation

    public WardenStyleAttackGoal(AIWardenEntity warden) {
        this.warden = warden;
        this.setFlags(EnumSet.of(Goal.Flag.MOVE, Goal.Flag.LOOK));
    }

    @Override
    public boolean canUse() {
        this.target = this.warden.getTarget();
        if (this.target == null || !this.target.isAlive()) {
            return false;
        }

        // Activate when very close to target (like original Warden)
        double distance = this.warden.distanceTo(this.target);
        double heightDifference = Math.abs(this.target.getY() - this.warden.getY());

        // Activate when close and at same level (original Warden behavior)
        boolean shouldAttack = distance <= 6.0 && heightDifference <= 2.0;

        if (shouldAttack) {
            System.out.println("AI Warden: WARDEN-STYLE ATTACK activated! Distance: " + String.format("%.1f", distance) + ", Height diff: " + String.format("%.1f", heightDifference));
        }

        return shouldAttack;
    }

    @Override
    public boolean canContinueToUse() {
        if (this.target == null || !this.target.isAlive()) {
            return false;
        }

        double distance = this.warden.distanceTo(this.target);
        double heightDifference = Math.abs(this.target.getY() - this.warden.getY());

        // Continue attacking while close
        return distance <= 8.0 && heightDifference <= 3.0;
    }

    @Override
    public void start() {
        this.attackTimer = 0;
        this.sonicBoomCooldown = 0;
        this.emergingTimer = 0;
        this.isEmerging = true; // Start with emerging behavior like original Warden
        
        // Play emerging sound like original Warden
        this.warden.playSound(SoundEvents.WARDEN_EMERGE, 2.0F, 1.0F);
        System.out.println("AI Warden: Starting WARDEN-STYLE ATTACK - emerging from ground!");
    }

    @Override
    public void stop() {
        this.warden.getNavigation().stop();
        this.attackTimer = 0;
        this.isEmerging = false;
        System.out.println("AI Warden: Stopping Warden-style attack");
    }

    @Override
    public void tick() {
        if (this.target == null) {
            return;
        }

        this.attackTimer++;
        if (this.sonicBoomCooldown > 0) {
            this.sonicBoomCooldown--;
        }

        // Always look at target aggressively
        this.warden.getLookControl().setLookAt(this.target, 30.0F, 30.0F);

        double distance = this.warden.distanceTo(this.target);

        // EMERGING BEHAVIOR (like original Warden when it first detects you)
        if (this.isEmerging && this.emergingTimer < EMERGING_DURATION) {
            this.emergingTimer++;
            performEmergingBehavior();
            return;
        } else if (this.isEmerging) {
            this.isEmerging = false;
            System.out.println("AI Warden: Finished emerging - beginning aggressive attacks!");
        }

        // AGGRESSIVE MOVEMENT (like original Warden)
        if (this.attackTimer % 5 == 0) { // Update movement frequently
            this.warden.getNavigation().moveTo(this.target, 1.3); // Fast aggressive movement
        }

        // MELEE ATTACKS (when very close)
        if (distance <= 3.0 && this.attackTimer % MELEE_ATTACK_INTERVAL == 0) {
            performMeleeAttack();
        }

        // SONIC BOOM ATTACKS (medium range, like original Warden)
        if (distance > 3.0 && distance <= 6.0 && this.sonicBoomCooldown <= 0) {
            performSonicBoomAttack();
            this.sonicBoomCooldown = SONIC_BOOM_INTERVAL;
        }

        // AGGRESSIVE SOUNDS (like original Warden)
        if (this.attackTimer % 60 == 0) { // Every 3 seconds
            this.warden.playSound(SoundEvents.WARDEN_ANGRY, 1.5F, 0.8F);
            System.out.println("AI Warden: Aggressive roar during combat!");
        }

        // CHARGE ATTACK (when target tries to escape)
        if (this.target instanceof Player player) {
            double playerSpeed = player.getDeltaMovement().horizontalDistanceSqr();
            if (playerSpeed > 0.02 && distance > 4.0 && distance <= 8.0) {
                performChargeAttack();
            }
        }
    }

    private void performEmergingBehavior() {
        // Stop movement during emerging
        this.warden.getNavigation().stop();
        this.warden.setDeltaMovement(0, this.warden.getDeltaMovement().y, 0);

        // Dramatic emerging effects
        if (this.emergingTimer % 10 == 0) {
            this.warden.playSound(SoundEvents.WARDEN_HEARTBEAT, 1.0F, 0.5F);
        }

        // Look menacingly at target
        this.warden.getLookControl().setLookAt(this.target, 30.0F, 30.0F);

        System.out.println("AI Warden: Emerging... " + this.emergingTimer + "/" + EMERGING_DURATION);
    }

    private void performMeleeAttack() {
        if (this.target == null) {
            return;
        }

        // Face target
        this.warden.getLookControl().setLookAt(this.target, 30.0F, 30.0F);

        // Perform attack with original Warden damage
        boolean attackSuccess = this.warden.doHurtTarget(this.target);

        if (attackSuccess) {
            // Knockback like original Warden
            double deltaX = this.target.getX() - this.warden.getX();
            double deltaZ = this.target.getZ() - this.warden.getZ();
            double distance = Math.sqrt(deltaX * deltaX + deltaZ * deltaZ);

            if (distance > 0) {
                // Strong knockback
                this.target.setDeltaMovement(
                    this.target.getDeltaMovement().x + (deltaX / distance) * 1.5,
                    this.target.getDeltaMovement().y + 0.4,
                    this.target.getDeltaMovement().z + (deltaZ / distance) * 1.5
                );
            }

            // Attack sound
            this.warden.playSound(SoundEvents.WARDEN_ATTACK_IMPACT, 1.5F, 1.0F);
            System.out.println("AI Warden: Powerful melee attack with knockback!");
        }
    }

    private void performSonicBoomAttack() {
        if (this.target == null) {
            return;
        }

        // Face target for sonic boom
        this.warden.getLookControl().setLookAt(this.target, 30.0F, 30.0F);

        // Stop movement for sonic boom
        this.warden.getNavigation().stop();
        this.warden.setDeltaMovement(0, this.warden.getDeltaMovement().y, 0);

        // Trigger our custom sonic boom (respects damage multiplier)
        this.warden.performCustomSonicBoom();

        System.out.println("AI Warden: Sonic boom attack like original Warden!");
    }

    private void performChargeAttack() {
        if (this.target == null) {
            return;
        }

        // Calculate charge direction
        double deltaX = this.target.getX() - this.warden.getX();
        double deltaZ = this.target.getZ() - this.warden.getZ();
        double distance = Math.sqrt(deltaX * deltaX + deltaZ * deltaZ);

        if (distance > 0) {
            // Powerful charge toward target
            double chargeSpeed = 1.0;
            this.warden.setDeltaMovement(
                (deltaX / distance) * chargeSpeed,
                0.2, // Small upward boost
                (deltaZ / distance) * chargeSpeed
            );

            // Charge sound
            this.warden.playSound(SoundEvents.WARDEN_ROAR, 2.0F, 0.6F);
            System.out.println("AI Warden: Charging at escaping target like original Warden!");
        }
    }
}
