package com.aiwardenmod.entity.ai;

import com.aiwardenmod.entity.custom.AIWardenEntity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.ai.goal.Goal;

import java.util.EnumSet;

public class FlyToTargetGoal extends Goal {
    private final AIWardenEntity warden;
    private LivingEntity target;
    private int flyAttemptCooldown = 0;
    private static final int FLY_ATTEMPT_INTERVAL = 100; // 5 seconds

    public FlyToTargetGoal(AIWardenEntity warden) {
        this.warden = warden;
        this.setFlags(EnumSet.of(Goal.Flag.MOVE, Goal.Flag.LOOK));
    }

    @Override
    public boolean canUse() {
        this.target = this.warden.getTarget();
        if (this.target == null || !this.target.isAlive()) {
            return false;
        }

        // Only use this goal for extremely high targets (building should handle most cases)
        double heightDifference = this.target.getY() - this.warden.getY();
        double horizontalDistance = this.warden.distanceTo(this.target);

        return this.warden.canFly() &&
               heightDifference > 8.0 && // Much higher threshold
               horizontalDistance < 20.0 &&
               horizontalDistance > 3.0;
    }

    @Override
    public boolean canContinueToUse() {
        if (this.target == null || !this.target.isAlive()) {
            return false;
        }

        double heightDifference = this.target.getY() - this.warden.getY();
        double horizontalDistance = this.warden.distanceTo(this.target);

        return heightDifference > 3.0 && horizontalDistance < 25.0 && this.flyAttemptCooldown > 0;
    }

    @Override
    public void start() {
        this.flyAttemptCooldown = 0;
    }

    @Override
    public void stop() {
        this.target = null;
        this.flyAttemptCooldown = 0;
    }

    @Override
    public void tick() {
        if (this.target == null) {
            return;
        }

        if (this.flyAttemptCooldown > 0) {
            this.flyAttemptCooldown--;
            return;
        }

        // Check if we should attempt to fly
        if (shouldAttemptFly()) {
            if (this.warden.tryFlyToTarget(this.target)) {
                this.flyAttemptCooldown = FLY_ATTEMPT_INTERVAL;
            } else {
                this.flyAttemptCooldown = 40; // Shorter cooldown if fly failed
            }
        }
    }

    private boolean shouldAttemptFly() {
        if (this.target == null) {
            return false;
        }

        double heightDifference = this.target.getY() - this.warden.getY();
        double horizontalDistance = this.warden.distanceTo(this.target);

        // Only fly for extremely high targets that building can't handle
        if (heightDifference > 10.0 && horizontalDistance < 15.0) {
            return true;
        }

        // Also fly if target is very high and we've been stuck for a long time
        if (heightDifference > 8.0 && this.warden.getNavigation().isStuck()) {
            return true;
        }

        return false;
    }
}
