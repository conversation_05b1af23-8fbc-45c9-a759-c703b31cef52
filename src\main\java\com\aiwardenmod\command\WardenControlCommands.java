package com.aiwardenmod.command;

import com.aiwardenmod.entity.custom.AIWardenEntity;
import com.mojang.brigadier.CommandDispatcher;
import com.mojang.brigadier.arguments.DoubleArgumentType;
import com.mojang.brigadier.arguments.IntegerArgumentType;
import com.mojang.brigadier.context.CommandContext;
import net.minecraft.commands.CommandSourceStack;
import net.minecraft.commands.Commands;
import net.minecraft.network.chat.Component;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.phys.AABB;

import java.util.List;

public class WardenControlCommands {

    public static void register(CommandDispatcher<CommandSourceStack> dispatcher) {
        // Main warden command with subcommands
        dispatcher.register(Commands.literal("warden")
            .requires(source -> source.hasPermission(2)) // Requires OP level 2

            // Damage scaling command
            .then(Commands.literal("damage")
                .then(Commands.argument("multiplier", DoubleArgumentType.doubleArg(0.0, 5.0))
                    .then(Commands.argument("radius", DoubleArgumentType.doubleArg(1.0, 200.0))
                        .executes(context -> setDamageScale(context,
                            DoubleArgumentType.getDouble(context, "multiplier"),
                            DoubleArgumentType.getDouble(context, "radius"))))
                    .executes(context -> setDamageScale(context,
                        DoubleArgumentType.getDouble(context, "multiplier"), 150.0))))

            // Building toggle command
            .then(Commands.literal("building")
                .then(Commands.literal("on")
                    .then(Commands.argument("radius", DoubleArgumentType.doubleArg(1.0, 200.0))
                        .executes(context -> setBuildingEnabled(context, true, DoubleArgumentType.getDouble(context, "radius"))))
                    .executes(context -> setBuildingEnabled(context, true, 150.0)))
                .then(Commands.literal("off")
                    .then(Commands.argument("radius", DoubleArgumentType.doubleArg(1.0, 200.0))
                        .executes(context -> setBuildingEnabled(context, false, DoubleArgumentType.getDouble(context, "radius"))))
                    .executes(context -> setBuildingEnabled(context, false, 150.0))))

            // Special attack commands
            .then(Commands.literal("slam")
                .then(Commands.argument("radius", DoubleArgumentType.doubleArg(1.0, 200.0))
                    .executes(context -> triggerAerialSlam(context, DoubleArgumentType.getDouble(context, "radius"))))
                .executes(context -> triggerAerialSlam(context, 150.0)))

            .then(Commands.literal("rage")
                .then(Commands.argument("duration", IntegerArgumentType.integer(5, 300))
                    .then(Commands.argument("radius", DoubleArgumentType.doubleArg(1.0, 200.0))
                        .executes(context -> triggerRageMode(context,
                            IntegerArgumentType.getInteger(context, "duration"),
                            DoubleArgumentType.getDouble(context, "radius"))))
                    .executes(context -> triggerRageMode(context,
                        IntegerArgumentType.getInteger(context, "duration"), 150.0)))
                .executes(context -> triggerRageMode(context, 60, 150.0)))

            .then(Commands.literal("teleport")
                .then(Commands.argument("radius", DoubleArgumentType.doubleArg(1.0, 200.0))
                    .executes(context -> triggerTeleportStrike(context, DoubleArgumentType.getDouble(context, "radius"))))
                .executes(context -> triggerTeleportStrike(context, 150.0)))

            .then(Commands.literal("earthquake")
                .then(Commands.argument("intensity", IntegerArgumentType.integer(1, 10))
                    .then(Commands.argument("radius", DoubleArgumentType.doubleArg(1.0, 200.0))
                        .executes(context -> triggerEarthquake(context,
                            IntegerArgumentType.getInteger(context, "intensity"),
                            DoubleArgumentType.getDouble(context, "radius"))))
                    .executes(context -> triggerEarthquake(context,
                        IntegerArgumentType.getInteger(context, "intensity"), 150.0)))
                .executes(context -> triggerEarthquake(context, 5, 150.0)))

            .then(Commands.literal("speed")
                .then(Commands.argument("multiplier", DoubleArgumentType.doubleArg(0.1, 5.0))
                    .then(Commands.argument("radius", DoubleArgumentType.doubleArg(1.0, 200.0))
                        .executes(context -> setSpeedMultiplier(context,
                            DoubleArgumentType.getDouble(context, "multiplier"),
                            DoubleArgumentType.getDouble(context, "radius"))))
                    .executes(context -> setSpeedMultiplier(context,
                        DoubleArgumentType.getDouble(context, "multiplier"), 150.0))))

            .then(Commands.literal("info")
                .then(Commands.argument("radius", DoubleArgumentType.doubleArg(1.0, 200.0))
                    .executes(context -> showWardenInfo(context, DoubleArgumentType.getDouble(context, "radius"))))
                .executes(context -> showWardenInfo(context, 150.0)))
        );
    }

    private static int setDamageScale(CommandContext<CommandSourceStack> context, double multiplier, double radius) {
        CommandSourceStack source = context.getSource();

        if (!(source.getLevel() instanceof ServerLevel)) {
            source.sendFailure(Component.literal("This command can only be used in a server world!"));
            return 0;
        }

        ServerLevel serverLevel = (ServerLevel) source.getLevel();

        if (!(source.getEntity() instanceof ServerPlayer)) {
            source.sendFailure(Component.literal("This command can only be used by a player!"));
            return 0;
        }

        ServerPlayer player = (ServerPlayer) source.getEntity();

        List<AIWardenEntity> wardens = findWardens(serverLevel, player, radius);

        if (wardens.isEmpty()) {
            source.sendFailure(Component.literal("No AI Wardens found within " + radius + " blocks!"));
            return 0;
        }

        int affectedCount = 0;
        for (AIWardenEntity warden : wardens) {
            if (warden.isAlive()) {
                warden.setDamageMultiplier(multiplier);
                affectedCount++;
            }
        }

        final int finalCount = affectedCount;
        final double finalMultiplier = multiplier;
        source.sendSuccess(() -> Component.literal("Set damage multiplier to " + finalMultiplier + "x for " + finalCount + " AI Warden(s)!"), true);
        return affectedCount;
    }

    private static int setBuildingEnabled(CommandContext<CommandSourceStack> context, boolean enabled, double radius) {
        CommandSourceStack source = context.getSource();

        if (!(source.getLevel() instanceof ServerLevel)) {
            source.sendFailure(Component.literal("This command can only be used in a server world!"));
            return 0;
        }

        ServerLevel serverLevel = (ServerLevel) source.getLevel();

        if (!(source.getEntity() instanceof ServerPlayer)) {
            source.sendFailure(Component.literal("This command can only be used by a player!"));
            return 0;
        }

        ServerPlayer player = (ServerPlayer) source.getEntity();

        List<AIWardenEntity> wardens = findWardens(serverLevel, player, radius);

        if (wardens.isEmpty()) {
            source.sendFailure(Component.literal("No AI Wardens found within " + radius + " blocks!"));
            return 0;
        }

        int affectedCount = 0;
        for (AIWardenEntity warden : wardens) {
            if (warden.isAlive()) {
                warden.setBuildingEnabled(enabled);
                affectedCount++;
            }
        }

        final int finalCount = affectedCount;
        final boolean finalEnabled = enabled;
        source.sendSuccess(() -> Component.literal("Building " + (finalEnabled ? "ENABLED" : "DISABLED") + " for " + finalCount + " AI Warden(s)!"), true);
        return affectedCount;
    }

    private static int triggerAerialSlam(CommandContext<CommandSourceStack> context, double radius) {
        return executeSpecialAttack(context, radius, "aerial slam", AIWardenEntity::triggerAerialSlam);
    }

    private static int triggerRageMode(CommandContext<CommandSourceStack> context, int duration, double radius) {
        CommandSourceStack source = context.getSource();

        if (!(source.getLevel() instanceof ServerLevel)) {
            source.sendFailure(Component.literal("This command can only be used in a server world!"));
            return 0;
        }

        ServerLevel serverLevel = (ServerLevel) source.getLevel();

        if (!(source.getEntity() instanceof ServerPlayer)) {
            source.sendFailure(Component.literal("This command can only be used by a player!"));
            return 0;
        }

        ServerPlayer player = (ServerPlayer) source.getEntity();

        List<AIWardenEntity> wardens = findWardens(serverLevel, player, radius);

        if (wardens.isEmpty()) {
            source.sendFailure(Component.literal("No AI Wardens found within " + radius + " blocks!"));
            return 0;
        }

        int triggeredCount = 0;
        for (AIWardenEntity warden : wardens) {
            if (warden.isAlive()) {
                warden.triggerRageMode(duration);
                triggeredCount++;
            }
        }

        final int finalCount = triggeredCount;
        final int finalDuration = duration;
        source.sendSuccess(() -> Component.literal("Triggered rage mode (" + finalDuration + " seconds) for " + finalCount + " AI Warden(s)!"), true);
        return triggeredCount;
    }

    private static int triggerTeleportStrike(CommandContext<CommandSourceStack> context, double radius) {
        return executeSpecialAttack(context, radius, "teleport strike", AIWardenEntity::triggerTeleportStrike);
    }

    private static int triggerEarthquake(CommandContext<CommandSourceStack> context, int intensity, double radius) {
        CommandSourceStack source = context.getSource();

        if (!(source.getLevel() instanceof ServerLevel)) {
            source.sendFailure(Component.literal("This command can only be used in a server world!"));
            return 0;
        }

        ServerLevel serverLevel = (ServerLevel) source.getLevel();

        if (!(source.getEntity() instanceof ServerPlayer)) {
            source.sendFailure(Component.literal("This command can only be used by a player!"));
            return 0;
        }

        ServerPlayer player = (ServerPlayer) source.getEntity();

        List<AIWardenEntity> wardens = findWardens(serverLevel, player, radius);

        if (wardens.isEmpty()) {
            source.sendFailure(Component.literal("No AI Wardens found within " + radius + " blocks!"));
            return 0;
        }

        int triggeredCount = 0;
        for (AIWardenEntity warden : wardens) {
            if (warden.isAlive()) {
                warden.triggerEarthquake(intensity);
                triggeredCount++;
            }
        }

        final int finalCount = triggeredCount;
        final int finalIntensity = intensity;
        source.sendSuccess(() -> Component.literal("Triggered earthquake (intensity " + finalIntensity + ") for " + finalCount + " AI Warden(s)!"), true);
        return triggeredCount;
    }

    private static int setSpeedMultiplier(CommandContext<CommandSourceStack> context, double multiplier, double radius) {
        CommandSourceStack source = context.getSource();

        if (!(source.getLevel() instanceof ServerLevel)) {
            source.sendFailure(Component.literal("This command can only be used in a server world!"));
            return 0;
        }

        ServerLevel serverLevel = (ServerLevel) source.getLevel();

        if (!(source.getEntity() instanceof ServerPlayer)) {
            source.sendFailure(Component.literal("This command can only be used by a player!"));
            return 0;
        }

        ServerPlayer player = (ServerPlayer) source.getEntity();

        List<AIWardenEntity> wardens = findWardens(serverLevel, player, radius);

        if (wardens.isEmpty()) {
            source.sendFailure(Component.literal("No AI Wardens found within " + radius + " blocks!"));
            return 0;
        }

        int affectedCount = 0;
        for (AIWardenEntity warden : wardens) {
            if (warden.isAlive()) {
                warden.setSpeedMultiplier(multiplier);
                affectedCount++;
            }
        }

        final int finalCount = affectedCount;
        final double finalMultiplier = multiplier;
        source.sendSuccess(() -> Component.literal("Set speed multiplier to " + finalMultiplier + "x for " + finalCount + " AI Warden(s)!"), true);
        return affectedCount;
    }

    private static int showWardenInfo(CommandContext<CommandSourceStack> context, double radius) {
        CommandSourceStack source = context.getSource();

        if (!(source.getLevel() instanceof ServerLevel)) {
            source.sendFailure(Component.literal("This command can only be used in a server world!"));
            return 0;
        }

        ServerLevel serverLevel = (ServerLevel) source.getLevel();

        if (!(source.getEntity() instanceof ServerPlayer)) {
            source.sendFailure(Component.literal("This command can only be used by a player!"));
            return 0;
        }

        ServerPlayer player = (ServerPlayer) source.getEntity();

        List<AIWardenEntity> wardens = findWardens(serverLevel, player, radius);

        if (wardens.isEmpty()) {
            source.sendFailure(Component.literal("No AI Wardens found within " + radius + " blocks!"));
            return 0;
        }

        source.sendSuccess(() -> Component.literal("=== AI Warden Information ==="), false);
        for (int i = 0; i < wardens.size(); i++) {
            AIWardenEntity warden = wardens.get(i);
            final int wardenNum = i + 1;
            final double distance = player.distanceTo(warden);
            final double damageMultiplier = warden.getDamageMultiplier();
            final double speedMultiplier = warden.getSpeedMultiplier();
            final boolean inRageMode = warden.isInRageMode();

            source.sendSuccess(() -> Component.literal("Warden #" + wardenNum + ": Distance=" + String.format("%.1f", distance) +
                ", Damage=" + damageMultiplier + "x, Speed=" + speedMultiplier + "x, Rage=" + inRageMode), false);
        }

        return wardens.size();
    }

    private static int executeSpecialAttack(CommandContext<CommandSourceStack> context, double radius, String attackName, java.util.function.Consumer<AIWardenEntity> attackTrigger) {
        CommandSourceStack source = context.getSource();

        if (!(source.getLevel() instanceof ServerLevel)) {
            source.sendFailure(Component.literal("This command can only be used in a server world!"));
            return 0;
        }

        ServerLevel serverLevel = (ServerLevel) source.getLevel();

        if (!(source.getEntity() instanceof ServerPlayer)) {
            source.sendFailure(Component.literal("This command can only be used by a player!"));
            return 0;
        }

        ServerPlayer player = (ServerPlayer) source.getEntity();

        List<AIWardenEntity> wardens = findWardens(serverLevel, player, radius);

        if (wardens.isEmpty()) {
            source.sendFailure(Component.literal("No AI Wardens found within " + radius + " blocks!"));
            return 0;
        }

        int triggeredCount = 0;
        for (AIWardenEntity warden : wardens) {
            if (warden.isAlive()) {
                attackTrigger.accept(warden);
                triggeredCount++;
            }
        }

        final int finalCount = triggeredCount;
        source.sendSuccess(() -> Component.literal("Triggered " + attackName + " for " + finalCount + " AI Warden(s)!"), true);
        return triggeredCount;
    }

    private static List<AIWardenEntity> findWardens(ServerLevel serverLevel, ServerPlayer player, double radius) {
        AABB searchArea = new AABB(
            player.getX() - radius, player.getY() - radius, player.getZ() - radius,
            player.getX() + radius, player.getY() + radius, player.getZ() + radius
        );

        return serverLevel.getEntitiesOfClass(AIWardenEntity.class, searchArea);
    }
}
