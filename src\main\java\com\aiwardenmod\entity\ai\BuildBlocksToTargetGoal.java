package com.aiwardenmod.entity.ai;

import com.aiwardenmod.entity.custom.AIWardenEntity;
import net.minecraft.core.BlockPos;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.ai.goal.Goal;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.pathfinder.Path;
import net.minecraft.world.phys.Vec3;

import java.util.EnumSet;

public class BuildBlocksToTargetGoal extends Goal {
    private final AIWardenEntity warden;
    private final double speedModifier;
    private LivingEntity target;
    private int buildAttemptCooldown = 0;
    private int consecutiveFailures = 0;
    private BlockPos lastBuildPos = null;
    private int stuckCounter = 0;
    private java.util.List<BlockPos> plannedPath = new java.util.ArrayList<>();
    private int pathIndex = 0;
    private static final int BUILD_ATTEMPT_INTERVAL = 8; // 0.4 seconds - very fast building
    private static final int MAX_CONSECUTIVE_FAILURES = 3;

    public BuildBlocksToTargetGoal(AIWardenEntity warden, double speedModifier) {
        this.warden = warden;
        this.speedModifier = speedModifier;
        this.setFlags(EnumSet.of(Goal.Flag.MOVE));
    }

    @Override
    public boolean canUse() {
        this.target = this.warden.getTarget();
        if (this.target == null || !this.target.isAlive()) {
            return false;
        }

        // Much more aggressive building conditions
        double heightDifference = this.target.getY() - this.warden.getY();
        double distance = this.warden.distanceTo(this.target);

        // Build for ANY height difference over 1 block
        if (heightDifference < 1.0) {
            return false;
        }

        // Build even when close if target is higher
        if (distance > 25.0) {
            return false;
        }

        // Only let flying handle extremely high targets (15+ blocks)
        if (heightDifference > 15.0) {
            return false;
        }

        // Check if warden can build blocks
        boolean canBuild = this.warden.canBuildBlocks();

        // Always prioritize building over other behaviors when target is higher
        if (canBuild && heightDifference > 1.5) {
            System.out.println("AI Warden: AGGRESSIVE Building goal activated! Height diff: " + String.format("%.1f", heightDifference) + ", Distance: " + String.format("%.1f", distance));

            // Reset failure counters when starting fresh
            this.consecutiveFailures = 0;
            this.stuckCounter = 0;
            return true;
        }

        return false;
    }

    @Override
    public boolean canContinueToUse() {
        if (this.target == null || !this.target.isAlive()) {
            return false;
        }

        double heightDifference = this.target.getY() - this.warden.getY();
        double distance = this.warden.distanceTo(this.target);

        return heightDifference >= 1.5 && distance < 20.0 && distance > 2.0;
    }

    @Override
    public void start() {
        this.buildAttemptCooldown = 0;
    }

    @Override
    public void stop() {
        this.target = null;
        this.buildAttemptCooldown = 0;
    }

    @Override
    public void tick() {
        if (this.target == null) {
            return;
        }

        if (this.buildAttemptCooldown > 0) {
            this.buildAttemptCooldown--;
            return;
        }

        // Complex building strategy
        double heightDiff = this.target.getY() - this.warden.getY();
        double distance = this.warden.distanceTo(this.target);

        // Check if we're stuck in the same position
        BlockPos currentPos = this.warden.blockPosition();
        if (currentPos.equals(this.lastBuildPos)) {
            this.stuckCounter++;
            if (this.stuckCounter > 5) {
                // We're stuck, try a different approach
                System.out.println("AI Warden: Stuck in same position, trying alternative building strategy");
                this.stuckCounter = 0;
                this.lastBuildPos = null;
                tryAlternativeBuildingStrategy();
                return;
            }
        } else {
            this.stuckCounter = 0;
            this.lastBuildPos = currentPos;
        }

        // Try multiple building strategies in order of preference
        BlockPos buildPos = null;

        // Strategy 1: Build a direct path to target
        if (buildPos == null) {
            buildPos = findDirectPathBuildPosition();
        }

        // Strategy 2: Build stairs/ramp toward target
        if (buildPos == null) {
            buildPos = findStairsBuildPosition();
        }

        // Strategy 3: Build platform to get higher
        if (buildPos == null) {
            buildPos = findPlatformBuildPosition();
        }

        // Strategy 4: Emergency building (anywhere that helps)
        if (buildPos == null) {
            buildPos = findEmergencyBuildPosition();
        }

        if (buildPos != null && this.warden.tryBuildBlock(buildPos)) {
            this.buildAttemptCooldown = BUILD_ATTEMPT_INTERVAL;
            this.consecutiveFailures = 0;

            System.out.println("AI Warden: Successfully built block at " + buildPos + " (Height diff: " + String.format("%.1f", heightDiff) + ")");

            // Force movement toward the built block
            this.warden.getNavigation().moveTo(buildPos.getX() + 0.5, buildPos.getY() + 1.1, buildPos.getZ() + 0.5, this.speedModifier * 1.5);
        } else {
            this.consecutiveFailures++;

            if (this.consecutiveFailures >= MAX_CONSECUTIVE_FAILURES) {
                System.out.println("AI Warden: Too many build failures, trying to get closer to target");
                // Move closer to target and reset
                this.warden.getNavigation().moveTo(this.target, this.speedModifier);
                this.consecutiveFailures = 0;
                this.buildAttemptCooldown = 15; // Longer cooldown after failures
            } else {
                this.buildAttemptCooldown = 3; // Short cooldown for retry
            }
        }
    }

    private BlockPos findBuildPosition() {
        if (this.target == null) {
            return null;
        }

        Level level = this.warden.level();
        BlockPos wardenPos = this.warden.blockPosition();
        BlockPos targetPos = this.target.blockPosition();

        // Calculate direction towards target
        Vec3 direction = new Vec3(targetPos.getX() - wardenPos.getX(), 0, targetPos.getZ() - wardenPos.getZ()).normalize();

        // First, try building directly under the warden to lift it up
        BlockPos underWarden = wardenPos.below();
        if (isValidBuildPosition(underWarden) && underWarden.getY() >= wardenPos.getY() - 1) {
            System.out.println("AI Warden: Building directly under warden at " + underWarden);
            return underWarden;
        }

        // Next, try building right next to the warden in the direction of the target
        for (int distance = 1; distance <= 2; distance++) {
            BlockPos nextToWarden = wardenPos.offset((int)(direction.x * distance), 0, (int)(direction.z * distance));

            // Try at warden's level and one level up
            for (int heightOffset = 0; heightOffset <= 1; heightOffset++) {
                BlockPos buildPos = nextToWarden.above(heightOffset);

                if (isValidBuildPosition(buildPos)) {
                    double targetHeight = this.target.getY();
                    double currentHeight = this.warden.getY();
                    double buildHeight = buildPos.getY() + 1; // +1 because warden will stand on top

                    if (buildHeight > currentHeight && buildHeight <= targetHeight + 2) {
                        System.out.println("AI Warden: Building next to warden at " + buildPos);
                        return buildPos;
                    }
                }
            }
        }

        // Finally, try positions in a small area around the warden
        for (int attempts = 0; attempts < 4; attempts++) {
            double angle = (attempts * Math.PI / 2.0); // 4 directions around the warden
            double offsetX = Math.cos(angle) * 1.0;
            double offsetZ = Math.sin(angle) * 1.0;

            BlockPos candidatePos = wardenPos.offset((int)offsetX, 0, (int)offsetZ);

            // Check multiple heights
            for (int heightOffset = 0; heightOffset <= 2; heightOffset++) {
                BlockPos buildPos = candidatePos.above(heightOffset);

                if (isValidBuildPosition(buildPos)) {
                    double targetHeight = this.target.getY();
                    double currentHeight = this.warden.getY();
                    double buildHeight = buildPos.getY() + 1; // +1 because warden will stand on top

                    if (buildHeight > currentHeight && buildHeight <= targetHeight + 2) {
                        return buildPos;
                    }
                }
            }
        }

        return null;
    }

    // Complex building strategies
    private void tryAlternativeBuildingStrategy() {
        // Try building in a different direction or pattern
        BlockPos wardenPos = this.warden.blockPosition();

        // Try building in cardinal directions
        BlockPos[] alternatives = {
            wardenPos.north(),
            wardenPos.south(),
            wardenPos.east(),
            wardenPos.west(),
            wardenPos.above(),
            wardenPos.north().above(),
            wardenPos.south().above(),
            wardenPos.east().above(),
            wardenPos.west().above()
        };

        for (BlockPos pos : alternatives) {
            if (isValidBuildPosition(pos)) {
                if (this.warden.tryBuildBlock(pos)) {
                    System.out.println("AI Warden: Alternative building successful at " + pos);
                    this.buildAttemptCooldown = BUILD_ATTEMPT_INTERVAL;
                    return;
                }
            }
        }
    }

    private BlockPos findDirectPathBuildPosition() {
        if (this.target == null) return null;

        BlockPos wardenPos = this.warden.blockPosition();
        BlockPos targetPos = this.target.blockPosition();

        // Calculate direct line to target
        Vec3 direction = new Vec3(
            targetPos.getX() - wardenPos.getX(),
            0,
            targetPos.getZ() - wardenPos.getZ()
        ).normalize();

        // Try positions along the direct path
        for (int distance = 1; distance <= 3; distance++) {
            BlockPos pathPos = wardenPos.offset(
                (int)(direction.x * distance),
                0,
                (int)(direction.z * distance)
            );

            // Try at current level and one level up
            for (int height = 0; height <= 2; height++) {
                BlockPos buildPos = pathPos.above(height);
                if (isValidBuildPosition(buildPos) &&
                    buildPos.getY() > wardenPos.getY() - 1 &&
                    buildPos.getY() <= this.target.getY() + 1) {
                    return buildPos;
                }
            }
        }

        return null;
    }

    private BlockPos findStairsBuildPosition() {
        if (this.target == null) return null;

        BlockPos wardenPos = this.warden.blockPosition();
        BlockPos targetPos = this.target.blockPosition();

        // Build stairs toward target
        Vec3 direction = new Vec3(
            targetPos.getX() - wardenPos.getX(),
            0,
            targetPos.getZ() - wardenPos.getZ()
        ).normalize();

        // Calculate how many steps we need
        double heightDiff = this.target.getY() - this.warden.getY();
        int stepsNeeded = (int) Math.ceil(heightDiff);

        for (int step = 1; step <= Math.min(stepsNeeded, 4); step++) {
            BlockPos stepPos = wardenPos.offset(
                (int)(direction.x * step),
                step - 1,
                (int)(direction.z * step)
            );

            if (isValidBuildPosition(stepPos)) {
                return stepPos;
            }
        }

        return null;
    }

    private BlockPos findPlatformBuildPosition() {
        if (this.target == null) return null;

        BlockPos wardenPos = this.warden.blockPosition();

        // Build a platform around the warden to get higher
        BlockPos[] platformPositions = {
            wardenPos.above(),
            wardenPos.north().above(),
            wardenPos.south().above(),
            wardenPos.east().above(),
            wardenPos.west().above(),
            wardenPos.above(2),
            wardenPos.north().above(2),
            wardenPos.south().above(2),
            wardenPos.east().above(2),
            wardenPos.west().above(2)
        };

        for (BlockPos pos : platformPositions) {
            if (isValidBuildPosition(pos) && pos.getY() > wardenPos.getY()) {
                return pos;
            }
        }

        return null;
    }

    private BlockPos findEmergencyBuildPosition() {
        if (this.target == null) return null;

        BlockPos wardenPos = this.warden.blockPosition();

        // Emergency: build anywhere that might help
        for (int x = -2; x <= 2; x++) {
            for (int z = -2; z <= 2; z++) {
                for (int y = 0; y <= 3; y++) {
                    BlockPos emergencyPos = wardenPos.offset(x, y, z);

                    if (isValidBuildPosition(emergencyPos) &&
                        emergencyPos.getY() >= wardenPos.getY()) {
                        System.out.println("AI Warden: Using emergency build position at " + emergencyPos);
                        return emergencyPos;
                    }
                }
            }
        }

        return null;
    }

    private boolean isValidBuildPosition(BlockPos pos) {
        Level level = this.warden.level();

        // Check if the position is empty
        if (!level.isEmptyBlock(pos)) {
            return false;
        }

        // Check if there's a solid block below (or we're building on ground level)
        BlockPos below = pos.below();
        if (!level.getBlockState(below).isSolidRender(level, below) && pos.getY() > level.getMinBuildHeight()) {
            return false;
        }

        // Check if there's enough space above for the warden
        if (!level.isEmptyBlock(pos.above()) || !level.isEmptyBlock(pos.above(2))) {
            return false;
        }

        // Make sure we're not building too high
        if (pos.getY() > this.warden.getY() + 5) {
            return false;
        }

        // Make sure we're not building too far from the warden
        double distance = this.warden.blockPosition().distSqr(pos);
        if (distance > 9.0) { // 3 block radius
            return false;
        }

        // Check if this position would actually help reach the target
        double currentDistanceToTarget = this.warden.distanceToSqr(this.target);
        double newDistanceToTarget = pos.distSqr(this.target.blockPosition());

        // Only build if it gets us closer or helps with height
        return newDistanceToTarget <= currentDistanceToTarget + 4.0; // Allow some tolerance
    }
}
