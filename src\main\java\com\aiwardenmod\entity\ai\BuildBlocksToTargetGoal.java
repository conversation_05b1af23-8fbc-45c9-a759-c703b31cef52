package com.aiwardenmod.entity.ai;

import com.aiwardenmod.entity.custom.AIWardenEntity;
import net.minecraft.core.BlockPos;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.ai.goal.Goal;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.pathfinder.Path;
import net.minecraft.world.phys.Vec3;

import java.util.EnumSet;

public class BuildBlocksToTargetGoal extends Goal {
    private final AIWardenEntity warden;
    private final double speedModifier;
    private LivingEntity target;
    private int buildAttemptCooldown = 0;
    private static final int BUILD_ATTEMPT_INTERVAL = 1; // 0.05 seconds - ULTRA fast building

    public BuildBlocksToTargetGoal(AIWardenEntity warden, double speedModifier) {
        this.warden = warden;
        this.speedModifier = speedModifier;
        // IMPORTANT: Control both MOVE and LOOK to keep warden stationary while building
        this.setFlags(EnumSet.of(Goal.Flag.MOVE, Goal.Flag.LOOK));
    }

    @Override
    public boolean canUse() {
        this.target = this.warden.getTarget();
        if (this.target == null || !this.target.isAlive()) {
            // Try to find a player even if no target is set
            this.target = findNearestPlayer();
            if (this.target == null) {
                return false;
            }
        }

        // EXTREMELY aggressive building conditions - build almost always when target is higher
        double heightDifference = this.target.getY() - this.warden.getY();
        double distance = this.warden.distanceTo(this.target);

        // Build for ANY height difference over 0.3 blocks, much larger range
        boolean shouldBuild = heightDifference > 0.3 &&
                             distance < 50.0 &&
                             this.warden.canBuildBlocks();

        if (shouldBuild) {
            System.out.println("AI Warden: ULTRA-AGGRESSIVE Building goal activated! Height diff: " + String.format("%.1f", heightDifference) + ", Distance: " + String.format("%.1f", distance));
        } else {
            System.out.println("AI Warden: Building NOT activated - Height diff: " + String.format("%.1f", heightDifference) + ", Distance: " + String.format("%.1f", distance) + ", Can build: " + this.warden.canBuildBlocks());
        }

        return shouldBuild;
    }

    private LivingEntity findNearestPlayer() {
        // Enhanced player detection - find players even through walls
        java.util.List<net.minecraft.world.entity.player.Player> players = this.warden.level().getEntitiesOfClass(
            net.minecraft.world.entity.player.Player.class,
            this.warden.getBoundingBox().inflate(50.0) // Large detection radius
        );

        net.minecraft.world.entity.player.Player nearestPlayer = null;
        double nearestDistance = Double.MAX_VALUE;

        for (net.minecraft.world.entity.player.Player player : players) {
            if (player.isSpectator() || player.isCreative()) {
                continue;
            }

            double distance = this.warden.distanceTo(player);
            if (distance < nearestDistance) {
                nearestDistance = distance;
                nearestPlayer = player;
            }
        }

        if (nearestPlayer != null) {
            System.out.println("AI Warden: Found player " + nearestPlayer.getName().getString() + " at distance " + String.format("%.1f", nearestDistance));
            // Set as target for the warden
            this.warden.setTarget(nearestPlayer);
        }

        return nearestPlayer;
    }

    @Override
    public boolean canContinueToUse() {
        if (this.target == null || !this.target.isAlive()) {
            return false;
        }

        double heightDifference = this.target.getY() - this.warden.getY();
        double distance = this.warden.distanceTo(this.target);

        return heightDifference >= 1.5 && distance < 20.0 && distance > 2.0;
    }

    @Override
    public void start() {
        this.buildAttemptCooldown = 0;
    }

    @Override
    public void stop() {
        this.target = null;
        this.buildAttemptCooldown = 0;
    }

    @Override
    public void tick() {
        if (this.target == null) {
            return;
        }

        if (this.buildAttemptCooldown > 0) {
            this.buildAttemptCooldown--;
            return;
        }

        // Check if we've reached the same level as target
        double heightDifference = this.target.getY() - this.warden.getY();

        if (heightDifference <= 1.0) {
            // We're at the same level! Switch to attack mode
            System.out.println("AI Warden: Reached target level! Switching to attack mode!");
            this.warden.setAttackMode(true);

            // Jump toward target for aggressive attack
            double deltaX = this.target.getX() - this.warden.getX();
            double deltaZ = this.target.getZ() - this.warden.getZ();
            double distance = Math.sqrt(deltaX * deltaX + deltaZ * deltaZ);

            if (distance > 0 && distance < 8.0) {
                // Powerful jump toward target
                double jumpStrength = 0.6;
                this.warden.setDeltaMovement(
                    (deltaX / distance) * 0.4, // Horizontal velocity toward target
                    jumpStrength, // Upward velocity
                    (deltaZ / distance) * 0.4
                );

                // Play attack sound
                this.warden.playSound(net.minecraft.sounds.SoundEvents.WARDEN_ATTACK_IMPACT, 1.5F, 0.8F);
                System.out.println("AI Warden: Jumping to attack target!");
            }

            // Stop building and let attack goals take over
            return;
        }

        // SMART TOWER BUILDING - build a proper tower toward target
        this.warden.getNavigation().stop();
        this.warden.setDeltaMovement(0, this.warden.getDeltaMovement().y, 0); // Keep Y velocity for gravity

        // Look at target while building
        this.warden.getLookControl().setLookAt(this.target, 30.0F, 30.0F);

        // Smart tower building strategy
        BlockPos buildPos = findTowerBuildPosition();

        if (buildPos != null && this.warden.tryBuildBlock(buildPos)) {
            this.buildAttemptCooldown = BUILD_ATTEMPT_INTERVAL;
            System.out.println("AI Warden: Built block at " + buildPos + " (staying stationary) - ULTRA FAST BUILDING!");

            // DON'T move - let the block lift the warden up naturally
            // The warden will automatically be pushed up by the block
        } else {
            // Try multiple positions rapidly if first fails
            BlockPos[] alternativePositions = {
                this.warden.blockPosition().below(),
                this.warden.blockPosition().north().below(),
                this.warden.blockPosition().south().below(),
                this.warden.blockPosition().east().below(),
                this.warden.blockPosition().west().below(),
                this.warden.blockPosition().above(),
                this.warden.blockPosition().north(),
                this.warden.blockPosition().south(),
                this.warden.blockPosition().east(),
                this.warden.blockPosition().west()
            };

            boolean builtSomething = false;
            for (BlockPos altPos : alternativePositions) {
                if (isValidBuildPosition(altPos) && this.warden.tryBuildBlock(altPos)) {
                    System.out.println("AI Warden: Built alternative block at " + altPos + " - PERSISTENT BUILDING!");
                    builtSomething = true;
                    break;
                }
            }

            if (!builtSomething) {
                this.buildAttemptCooldown = 2; // Very short cooldown for retry
                System.out.println("AI Warden: Failed to build anywhere, retrying in 0.1 seconds...");
            } else {
                this.buildAttemptCooldown = BUILD_ATTEMPT_INTERVAL;
            }
        }
    }

    private BlockPos findSimpleBuildPosition() {
        if (this.target == null) {
            return null;
        }

        BlockPos wardenPos = this.warden.blockPosition();
        BlockPos targetPos = this.target.blockPosition();

        // Calculate direction to target
        Vec3 direction = new Vec3(
            targetPos.getX() - wardenPos.getX(),
            0,
            targetPos.getZ() - wardenPos.getZ()
        ).normalize();

        // Try building 1-2 blocks toward target
        for (int distance = 1; distance <= 2; distance++) {
            BlockPos buildPos = wardenPos.offset(
                (int)(direction.x * distance),
                0,
                (int)(direction.z * distance)
            );

            if (isValidBuildPosition(buildPos)) {
                return buildPos;
            }

            // Also try one block higher
            BlockPos buildPosUp = buildPos.above();
            if (isValidBuildPosition(buildPosUp)) {
                return buildPosUp;
            }
        }

        // Try building directly under warden to lift up
        BlockPos underWarden = wardenPos.below();
        if (isValidBuildPosition(underWarden)) {
            return underWarden;
        }

        return null;
    }

    private BlockPos findTowerBuildPosition() {
        if (this.target == null) {
            return null;
        }

        BlockPos wardenPos = this.warden.blockPosition();
        BlockPos targetPos = this.target.blockPosition();
        double heightDiff = this.target.getY() - this.warden.getY();

        // Calculate direction toward target
        Vec3 direction = new Vec3(
            targetPos.getX() - wardenPos.getX(),
            0,
            targetPos.getZ() - wardenPos.getZ()
        ).normalize();

        // Strategy 1: Build directly under warden to lift up (MOST EFFECTIVE for height gain)
        if (heightDiff > 1.0) {
            BlockPos underWarden = wardenPos.below();
            if (isValidTowerPosition(underWarden) && canSafelyBuildUnder()) {
                System.out.println("AI Warden: Building under warden for lift at " + underWarden);
                return underWarden;
            }
        }

        // Strategy 2: Build stairs/ramp toward target
        for (int step = 1; step <= 3; step++) {
            BlockPos stepPos = wardenPos.offset(
                (int)(direction.x * step),
                step - 1, // Each step is one block higher
                (int)(direction.z * step)
            );

            if (isValidTowerPosition(stepPos) && !wouldTrapWarden(stepPos)) {
                System.out.println("AI Warden: Building stair step " + step + " at " + stepPos);
                return stepPos;
            }
        }

        // Strategy 3: Build adjacent blocks that are higher
        BlockPos[] adjacentUp = {
            wardenPos.north().above(),     // Build up and adjacent
            wardenPos.south().above(),
            wardenPos.east().above(),
            wardenPos.west().above(),
            wardenPos.north(),             // Build adjacent at same level
            wardenPos.south(),
            wardenPos.east(),
            wardenPos.west()
        };

        for (BlockPos pos : adjacentUp) {
            if (isValidTowerPosition(pos) && pos.getY() >= wardenPos.getY()) {
                System.out.println("AI Warden: Building adjacent up block at " + pos);
                return pos;
            }
        }

        // Strategy 4: Build further away if needed
        BlockPos[] furtherPositions = {
            wardenPos.north(2),
            wardenPos.south(2),
            wardenPos.east(2),
            wardenPos.west(2),
            wardenPos.north(2).above(),
            wardenPos.south(2).above(),
            wardenPos.east(2).above(),
            wardenPos.west(2).above()
        };

        for (BlockPos pos : furtherPositions) {
            if (isValidTowerPosition(pos)) {
                System.out.println("AI Warden: Building further away at " + pos);
                return pos;
            }
        }

        return null;
    }

    private boolean wouldTrapWarden(BlockPos buildPos) {
        BlockPos wardenPos = this.warden.blockPosition();

        // Only prevent building in the exact same space as warden
        if (buildPos.equals(wardenPos) || buildPos.equals(wardenPos.above())) {
            return true;
        }

        // Allow building adjacent - the stuck detection will handle any issues
        return false;
    }

    private boolean canSafelyBuildUnder() {
        BlockPos wardenPos = this.warden.blockPosition();

        // More lenient check - just make sure warden isn't already stuck in blocks
        if (!this.warden.level().isEmptyBlock(wardenPos)) {
            return false;
        }

        // Allow building under in most cases - the post-build teleport will handle positioning
        return true;
    }

    private boolean isValidTowerPosition(BlockPos pos) {
        Level level = this.warden.level();

        // Check if the position is empty
        if (!level.isEmptyBlock(pos)) {
            return false;
        }

        // For tower building, we need solid support below (unless we're building the base)
        BlockPos below = pos.below();
        boolean hasSolidSupport = !level.isEmptyBlock(below) ||
                                 level.getBlockState(below).isSolidRender(level, below) ||
                                 pos.getY() <= level.getMinBuildHeight() + 1;

        if (!hasSolidSupport) {
            return false;
        }

        // Check if there's enough space above for the warden (2 blocks high)
        if (!level.isEmptyBlock(pos.above()) || !level.isEmptyBlock(pos.above(2))) {
            return false;
        }

        // Don't build too far from the warden
        double distance = this.warden.blockPosition().distSqr(pos);
        if (distance > 16.0) { // 4 block radius
            return false;
        }

        return true;
    }

    private boolean isValidBuildPosition(BlockPos pos) {
        Level level = this.warden.level();

        // Check if the position is empty
        if (!level.isEmptyBlock(pos)) {
            return false;
        }

        // Check if there's a solid block below (or we're building on ground level)
        BlockPos below = pos.below();
        if (!level.getBlockState(below).isSolidRender(level, below) && pos.getY() > level.getMinBuildHeight()) {
            return false;
        }

        // Check if there's enough space above for the warden
        if (!level.isEmptyBlock(pos.above()) || !level.isEmptyBlock(pos.above(2))) {
            return false;
        }

        // Make sure we're not building too high
        if (pos.getY() > this.warden.getY() + 5) {
            return false;
        }

        // Make sure we're not building too far from the warden
        double distance = this.warden.blockPosition().distSqr(pos);
        if (distance > 9.0) { // 3 block radius
            return false;
        }

        // Check if this position would actually help reach the target
        double currentDistanceToTarget = this.warden.distanceToSqr(this.target);
        double newDistanceToTarget = pos.distSqr(this.target.blockPosition());

        // Only build if it gets us closer or helps with height
        return newDistanceToTarget <= currentDistanceToTarget + 4.0; // Allow some tolerance
    }
}
