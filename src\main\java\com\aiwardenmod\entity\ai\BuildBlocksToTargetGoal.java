package com.aiwardenmod.entity.ai;

import com.aiwardenmod.entity.custom.AIWardenEntity;
import net.minecraft.core.BlockPos;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.ai.goal.Goal;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.pathfinder.Path;
import net.minecraft.world.phys.Vec3;

import java.util.EnumSet;

public class BuildBlocksToTargetGoal extends Goal {
    private final AIWardenEntity warden;
    private final double speedModifier;
    private LivingEntity target;
    private int buildAttemptCooldown = 0;
    private static final int BUILD_ATTEMPT_INTERVAL = 1; // 0.05 seconds - ULTRA fast building

    public BuildBlocksToTargetGoal(AIWardenEntity warden, double speedModifier) {
        this.warden = warden;
        this.speedModifier = speedModifier;
        // IMPORTANT: Control both MOVE and LOOK to keep warden stationary while building
        this.setFlags(EnumSet.of(Goal.Flag.MOVE, Goal.Flag.LOOK));
    }

    @Override
    public boolean canUse() {
        this.target = this.warden.getTarget();
        if (this.target == null || !this.target.isAlive()) {
            // Try to find a player even if no target is set
            this.target = findNearestPlayer();
            if (this.target == null) {
                return false;
            }
        }

        // SMART building conditions - prioritize building when player is higher or far
        double heightDifference = this.target.getY() - this.warden.getY();
        double distance = this.warden.distanceTo(this.target);

        // Check if building is enabled first
        if (!this.warden.isBuildingEnabled()) {
            return false; // Building disabled by command
        }

        // Build when target is higher OR when we need to bridge forward at same level
        boolean shouldBuild = ((heightDifference > 2.0 && distance < 50.0) || // Target higher - build up (increased threshold)
                              (Math.abs(heightDifference) <= 2.0 && distance > 8.0 && distance < 40.0)) && // Same level but far - bridge forward (increased range)
                             this.warden.canBuildBlocks();

        // ENHANCED: Also build if we can't path to target (bridge needed)
        if (Math.abs(heightDifference) <= 2.0 && distance > 3.0 && distance < 40.0) {
            // Check if we can actually path to the target
            boolean canPath = this.warden.getNavigation().createPath(this.target, 0) != null;
            if (!canPath) {
                shouldBuild = true;
                System.out.println("AI Warden: Can't path to target - BRIDGE BUILDING activated! Distance: " + String.format("%.1f", distance));
            }
        }

        // STOP building if we're at same level and very close - let chase goals take over
        if (Math.abs(heightDifference) <= 2.0 && distance <= 8.0) {
            shouldBuild = false;
            System.out.println("AI Warden: At same level and close - STOPPING building to chase! Height diff: " + String.format("%.1f", heightDifference) + ", Distance: " + String.format("%.1f", distance));
        }

        if (shouldBuild) {
            System.out.println("AI Warden: ULTRA-AGGRESSIVE Building goal activated! Height diff: " + String.format("%.1f", heightDifference) + ", Distance: " + String.format("%.1f", distance));
        } else {
            System.out.println("AI Warden: Building NOT activated - Height diff: " + String.format("%.1f", heightDifference) + ", Distance: " + String.format("%.1f", distance) + ", Can build: " + this.warden.canBuildBlocks());
        }

        return shouldBuild;
    }

    private LivingEntity findNearestPlayer() {
        // MASSIVE player detection - find players from very far away
        java.util.List<net.minecraft.world.entity.player.Player> players = this.warden.level().getEntitiesOfClass(
            net.minecraft.world.entity.player.Player.class,
            this.warden.getBoundingBox().inflate(150.0) // MASSIVE detection radius - 150 blocks!
        );

        net.minecraft.world.entity.player.Player nearestPlayer = null;
        double nearestDistance = Double.MAX_VALUE;

        for (net.minecraft.world.entity.player.Player player : players) {
            if (player.isSpectator() || player.isCreative()) {
                continue;
            }

            double distance = this.warden.distanceTo(player);
            if (distance < nearestDistance) {
                nearestDistance = distance;
                nearestPlayer = player;
            }
        }

        if (nearestPlayer != null) {
            System.out.println("AI Warden: Found player " + nearestPlayer.getName().getString() + " at distance " + String.format("%.1f", nearestDistance));
            // Set as target for the warden
            this.warden.setTarget(nearestPlayer);
        }

        return nearestPlayer;
    }

    @Override
    public boolean canContinueToUse() {
        if (this.target == null || !this.target.isAlive()) {
            return false;
        }

        double heightDifference = this.target.getY() - this.warden.getY();
        double distance = this.warden.distanceTo(this.target);

        return heightDifference >= 1.5 && distance < 20.0 && distance > 2.0;
    }

    @Override
    public void start() {
        this.buildAttemptCooldown = 0;
    }

    @Override
    public void stop() {
        this.target = null;
        this.buildAttemptCooldown = 0;
    }

    @Override
    public void tick() {
        if (this.target == null) {
            return;
        }

        if (this.buildAttemptCooldown > 0) {
            this.buildAttemptCooldown--;
            return;
        }

        // Check if we've reached the same level as target
        double heightDifference = this.target.getY() - this.warden.getY();

        if (heightDifference <= 1.0) {
            // We're at the same level! Switch to attack mode
            System.out.println("AI Warden: Reached target level! Switching to attack mode!");
            this.warden.setAttackMode(true);

            // Jump toward target for aggressive attack
            double deltaX = this.target.getX() - this.warden.getX();
            double deltaZ = this.target.getZ() - this.warden.getZ();
            double distance = Math.sqrt(deltaX * deltaX + deltaZ * deltaZ);

            if (distance > 0 && distance < 8.0) {
                // Powerful jump toward target
                double jumpStrength = 0.6;
                this.warden.setDeltaMovement(
                    (deltaX / distance) * 0.4, // Horizontal velocity toward target
                    jumpStrength, // Upward velocity
                    (deltaZ / distance) * 0.4
                );

                // Play attack sound
                this.warden.playSound(net.minecraft.sounds.SoundEvents.WARDEN_ATTACK_IMPACT, 1.5F, 0.8F);
                System.out.println("AI Warden: Jumping to attack target!");
            }

            // Stop building and let attack goals take over
            return;
        }

        // PERFECT BUILDING SYSTEM - analyze situation and choose strategy
        double horizontalDistance = Math.sqrt(
            Math.pow(this.target.getX() - this.warden.getX(), 2) +
            Math.pow(this.target.getZ() - this.warden.getZ(), 2)
        );

        BlockPos buildPos = null;
        String strategy = "";

        // INTELLIGENT STRATEGY SELECTION with detailed analysis
        System.out.println("AI Warden: Analyzing situation - Height diff: " + String.format("%.1f", heightDifference) + ", Horizontal dist: " + String.format("%.1f", horizontalDistance));

        if (heightDifference > 3.0) {
            // Target is significantly higher - prioritize tower building
            buildPos = findPerfectTowerPosition();
            strategy = "TOWER";
            System.out.println("AI Warden: Target much higher - using TOWER strategy");
        } else if (heightDifference >= -1.0 && heightDifference <= 2.0 && horizontalDistance > 10.0) {
            // Target is at similar height but far away - build bridge
            buildPos = findBridgeBuildPosition();
            strategy = "BRIDGE";
            System.out.println("AI Warden: Target far horizontally - using BRIDGE strategy");
        } else if (heightDifference > 0.5) {
            // Target is somewhat higher - use tower building
            buildPos = findPerfectTowerPosition();
            strategy = "TOWER";
            System.out.println("AI Warden: Target higher - using TOWER strategy");
        } else {
            // Complex situation or target lower - use adaptive building
            buildPos = findAdaptiveBuildPosition();
            strategy = "ADAPTIVE";
            System.out.println("AI Warden: Complex situation - using ADAPTIVE strategy");
        }

        // SMART BUILDING WITH FLYING
        if (heightDifference > 1.5) {
            // Target is higher - build upward with flying
            executeUpwardBuilding();
        } else if (Math.abs(heightDifference) <= 2.0) {
            // Same level - build forward bridge
            executeForwardBuilding();
        }
    }

    private BlockPos findSimpleBuildPosition() {
        if (this.target == null) {
            return null;
        }

        BlockPos wardenPos = this.warden.blockPosition();
        BlockPos targetPos = this.target.blockPosition();

        // Calculate direction to target
        Vec3 direction = new Vec3(
            targetPos.getX() - wardenPos.getX(),
            0,
            targetPos.getZ() - wardenPos.getZ()
        ).normalize();

        // Try building 1-2 blocks toward target
        for (int distance = 1; distance <= 2; distance++) {
            BlockPos buildPos = wardenPos.offset(
                (int)(direction.x * distance),
                0,
                (int)(direction.z * distance)
            );

            if (isValidBuildPosition(buildPos)) {
                return buildPos;
            }

            // Also try one block higher
            BlockPos buildPosUp = buildPos.above();
            if (isValidBuildPosition(buildPosUp)) {
                return buildPosUp;
            }
        }

        // Try building directly under warden to lift up
        BlockPos underWarden = wardenPos.below();
        if (isValidBuildPosition(underWarden)) {
            return underWarden;
        }

        return null;
    }

    // SMART TOWER BUILDING - Build toward player while gaining height
    private BlockPos findPerfectTowerPosition() {
        BlockPos wardenPos = this.warden.blockPosition();
        BlockPos targetPos = this.target.blockPosition();

        // Calculate direction toward target
        Vec3 direction = new Vec3(
            targetPos.getX() - wardenPos.getX(),
            0,
            targetPos.getZ() - wardenPos.getZ()
        ).normalize();

        // Strategy 1: Build stairs toward target while gaining height
        for (int step = 1; step <= 2; step++) {
            BlockPos stairPos = wardenPos.offset(
                (int)(direction.x * step),
                step - 1, // Each step is one block higher
                (int)(direction.z * step)
            );

            if (isPerfectBuildPosition(stairPos)) {
                System.out.println("AI Warden: Building stair toward target at step " + step);
                return stairPos;
            }
        }

        // Strategy 2: Build directly under warden for pure height gain
        BlockPos underWarden = wardenPos.below();
        if (isPerfectBuildPosition(underWarden)) {
            System.out.println("AI Warden: Building under self for height gain");
            return underWarden;
        }

        // Strategy 3: Build adjacent toward target direction
        BlockPos[] targetDirectionPositions = {
            wardenPos.offset((int)direction.x, -1, 0), // Toward target X
            wardenPos.offset(0, -1, (int)direction.z), // Toward target Z
            wardenPos.offset((int)direction.x, 0, 0),  // Same level toward X
            wardenPos.offset(0, 0, (int)direction.z)   // Same level toward Z
        };

        for (BlockPos pos : targetDirectionPositions) {
            if (isPerfectBuildPosition(pos)) {
                System.out.println("AI Warden: Building toward target direction");
                return pos;
            }
        }

        return null;
    }

    private BlockPos findBridgeBuildPosition() {
        BlockPos wardenPos = this.warden.blockPosition();
        BlockPos targetPos = this.target.blockPosition();

        // Calculate precise direction toward target
        Vec3 direction = new Vec3(
            targetPos.getX() - wardenPos.getX(),
            0,
            targetPos.getZ() - wardenPos.getZ()
        ).normalize();

        // SMART BRIDGE BUILDING - build stable platform first, then extend

        // First priority: Build platform under warden's feet for stability
        BlockPos underWarden = wardenPos.below();
        if (isPerfectBuildPosition(underWarden)) {
            System.out.println("AI Warden: Building bridge foundation under self");
            return underWarden;
        }

        // Second priority: Build bridge extension toward target
        for (int step = 1; step <= 3; step++) {
            // Try building at same level first (stable bridge)
            BlockPos bridgePos = wardenPos.offset(
                (int)(direction.x * step),
                0, // Same level as warden
                (int)(direction.z * step)
            );

            if (isBridgeBuildPosition(bridgePos)) {
                System.out.println("AI Warden: Building bridge extension step " + step + " toward target");
                return bridgePos;
            }

            // If same level doesn't work, try one block higher
            BlockPos bridgePosUp = bridgePos.above();
            if (isBridgeBuildPosition(bridgePosUp)) {
                System.out.println("AI Warden: Building elevated bridge step " + step + " toward target");
                return bridgePosUp;
            }
        }

        // Third priority: Build support pillars if needed
        for (int step = 1; step <= 2; step++) {
            BlockPos pillarPos = wardenPos.offset(
                (int)(direction.x * step),
                -1, // One block below for support
                (int)(direction.z * step)
            );

            if (isPerfectBuildPosition(pillarPos)) {
                System.out.println("AI Warden: Building bridge support pillar at step " + step);
                return pillarPos;
            }
        }

        return null;
    }

    private boolean isBridgeBuildPosition(BlockPos pos) {
        Level level = this.warden.level();

        // Must be empty
        if (!level.isEmptyBlock(pos)) {
            return false;
        }

        // Don't build in warden's exact position
        BlockPos wardenPos = this.warden.blockPosition();
        if (pos.equals(wardenPos) || pos.equals(wardenPos.above())) {
            return false;
        }

        // For bridge building, we're more lenient about support
        // Allow building over gaps (that's the point of a bridge!)

        // Must have space above for warden
        if (!level.isEmptyBlock(pos.above()) || !level.isEmptyBlock(pos.above(2))) {
            return false;
        }

        // Reasonable distance from warden
        double distance = wardenPos.distSqr(pos);
        if (distance > 16.0) { // 4 block radius for bridge building
            return false;
        }

        // Check if this position helps reach the target
        double currentDistanceToTarget = this.warden.distanceToSqr(this.target);
        double newDistanceToTarget = pos.distSqr(this.target.blockPosition());

        // Allow bridge building even if it doesn't immediately get closer
        // (bridges need to span gaps)
        return newDistanceToTarget <= currentDistanceToTarget + 8.0; // More tolerance for bridges
    }

    private BlockPos findAdaptiveBuildPosition() {
        BlockPos wardenPos = this.warden.blockPosition();

        // Try under warden first (most effective)
        BlockPos underWarden = wardenPos.below();
        if (isPerfectBuildPosition(underWarden)) {
            return underWarden;
        }

        // Try all adjacent positions
        BlockPos[] allAdjacent = {
            wardenPos.north(), wardenPos.south(), wardenPos.east(), wardenPos.west(),
            wardenPos.north().above(), wardenPos.south().above(),
            wardenPos.east().above(), wardenPos.west().above(),
            wardenPos.north().below(), wardenPos.south().below(),
            wardenPos.east().below(), wardenPos.west().below()
        };

        for (BlockPos pos : allAdjacent) {
            if (isPerfectBuildPosition(pos)) {
                return pos;
            }
        }

        return null;
    }

    private BlockPos findEmergencyBuildPosition() {
        BlockPos wardenPos = this.warden.blockPosition();

        // Try a wider radius for emergency building
        for (int x = -2; x <= 2; x++) {
            for (int z = -2; z <= 2; z++) {
                for (int y = -1; y <= 2; y++) {
                    if (x == 0 && z == 0 && y == 0) continue; // Skip warden position

                    BlockPos emergencyPos = wardenPos.offset(x, y, z);
                    if (isPerfectBuildPosition(emergencyPos)) {
                        return emergencyPos;
                    }
                }
            }
        }

        return null;
    }

    private void executeRobustBuild(BlockPos buildPos, String strategy) {
        // Special handling for under-building - FLY UP FIRST!
        if (buildPos.equals(this.warden.blockPosition().below())) {
            executeSmartUnderBuild(buildPos, strategy);
            return;
        }

        // For other builds, stay still and build
        this.warden.getNavigation().stop();
        this.warden.setDeltaMovement(0, this.warden.getDeltaMovement().y, 0);

        // Look at target
        this.warden.getLookControl().setLookAt(this.target, 30.0F, 30.0F);

        // Execute the build
        if (this.warden.tryBuildBlock(buildPos)) {
            this.buildAttemptCooldown = 1;
            System.out.println("AI Warden: " + strategy + " build successful at " + buildPos + "!");
        } else {
            this.buildAttemptCooldown = 2;
            System.out.println("AI Warden: " + strategy + " build failed at " + buildPos + ", retrying...");
        }
    }

    private void executeSmartUnderBuild(BlockPos buildPos, String strategy) {
        // SMART UNDER-BUILDING: Fly up, build below, land on block
        System.out.println("AI Warden: Executing smart under-build - flying up first!");

        // Step 1: Fly up to avoid getting stuck
        this.warden.setDeltaMovement(0, 0.8, 0); // Strong upward velocity
        this.warden.setNoGravity(true); // Temporarily disable gravity

        // Step 2: Build the block below while in air
        if (this.warden.tryBuildBlock(buildPos)) {
            System.out.println("AI Warden: " + strategy + " under-build successful while flying!");

            // Step 3: Land on the block we just built
            this.warden.setNoGravity(false); // Re-enable gravity
            this.warden.setDeltaMovement(0, -0.3, 0); // Controlled descent

            // Step 4: Navigate to center of built block
            this.warden.getNavigation().moveTo(buildPos.getX() + 0.5, buildPos.getY() + 1, buildPos.getZ() + 0.5, 1.0);

            this.buildAttemptCooldown = 1;
            System.out.println("AI Warden: Landing on built block at " + buildPos);
        } else {
            // Failed to build, land safely
            this.warden.setNoGravity(false);
            this.buildAttemptCooldown = 2;
            System.out.println("AI Warden: Under-build failed, landing safely");
        }
    }

    private boolean isPerfectBuildPosition(BlockPos pos) {
        Level level = this.warden.level();

        // Must be empty
        if (!level.isEmptyBlock(pos)) {
            return false;
        }

        // Don't build in warden's exact position
        BlockPos wardenPos = this.warden.blockPosition();
        if (pos.equals(wardenPos) || pos.equals(wardenPos.above())) {
            return false;
        }

        // Must have solid support below OR be building under warden
        BlockPos below = pos.below();
        boolean hasSolidSupport = !level.isEmptyBlock(below) ||
                                 pos.equals(wardenPos.below()) ||
                                 pos.getY() <= level.getMinBuildHeight() + 1;

        if (!hasSolidSupport) {
            return false;
        }

        // Must have space above for warden
        if (!level.isEmptyBlock(pos.above()) || !level.isEmptyBlock(pos.above(2))) {
            return false;
        }

        // Reasonable distance from warden
        double distance = wardenPos.distSqr(pos);
        if (distance > 25.0) { // 5 block radius
            return false;
        }

        // SMART VALIDATION: Prefer positions that help reach target
        if (this.target != null) {
            double currentDistanceToTarget = this.warden.distanceToSqr(this.target);
            double newDistanceToTarget = pos.distSqr(this.target.blockPosition());
            double heightDifference = this.target.getY() - this.warden.getY();

            // For tower building, prioritize height gain toward target
            if (heightDifference > 1.0) {
                // Allow building even if it doesn't immediately get closer horizontally
                // as long as it gains height
                if (pos.getY() >= wardenPos.getY()) {
                    return true; // Height gain is good
                }
            }

            // For bridge building, prioritize getting closer horizontally
            if (heightDifference <= 2.0 && currentDistanceToTarget > 8.0) {
                // Allow building if it gets us closer to target
                return newDistanceToTarget <= currentDistanceToTarget + 2.0;
            }
        }

        return true;
    }

    private BlockPos findTowerBuildPosition() {
        if (this.target == null) {
            return null;
        }

        BlockPos wardenPos = this.warden.blockPosition();
        BlockPos targetPos = this.target.blockPosition();
        double heightDiff = this.target.getY() - this.warden.getY();

        // Calculate direction toward target
        Vec3 direction = new Vec3(
            targetPos.getX() - wardenPos.getX(),
            0,
            targetPos.getZ() - wardenPos.getZ()
        ).normalize();

        // Strategy 1: Build directly under warden to lift up (MOST EFFECTIVE for height gain)
        if (heightDiff > 0.5) { // More aggressive threshold
            BlockPos underWarden = wardenPos.below();
            if (isValidTowerPosition(underWarden)) { // Removed canSafelyBuildUnder check
                System.out.println("AI Warden: Building under warden for lift at " + underWarden);
                return underWarden;
            }
        }

        // Strategy 2: Build stairs/ramp toward target
        for (int step = 1; step <= 3; step++) {
            BlockPos stepPos = wardenPos.offset(
                (int)(direction.x * step),
                step - 1, // Each step is one block higher
                (int)(direction.z * step)
            );

            if (isValidTowerPosition(stepPos) && !wouldTrapWarden(stepPos)) {
                System.out.println("AI Warden: Building stair step " + step + " at " + stepPos);
                return stepPos;
            }
        }

        // Strategy 3: Build adjacent blocks that are higher
        BlockPos[] adjacentUp = {
            wardenPos.north().above(),     // Build up and adjacent
            wardenPos.south().above(),
            wardenPos.east().above(),
            wardenPos.west().above(),
            wardenPos.north(),             // Build adjacent at same level
            wardenPos.south(),
            wardenPos.east(),
            wardenPos.west()
        };

        for (BlockPos pos : adjacentUp) {
            if (isValidTowerPosition(pos) && pos.getY() >= wardenPos.getY()) {
                System.out.println("AI Warden: Building adjacent up block at " + pos);
                return pos;
            }
        }

        // Strategy 4: Build further away if needed
        BlockPos[] furtherPositions = {
            wardenPos.north(2),
            wardenPos.south(2),
            wardenPos.east(2),
            wardenPos.west(2),
            wardenPos.north(2).above(),
            wardenPos.south(2).above(),
            wardenPos.east(2).above(),
            wardenPos.west(2).above()
        };

        for (BlockPos pos : furtherPositions) {
            if (isValidTowerPosition(pos)) {
                System.out.println("AI Warden: Building further away at " + pos);
                return pos;
            }
        }

        return null;
    }

    private boolean wouldTrapWarden(BlockPos buildPos) {
        BlockPos wardenPos = this.warden.blockPosition();

        // Only prevent building in the exact same space as warden
        if (buildPos.equals(wardenPos) || buildPos.equals(wardenPos.above())) {
            return true;
        }

        // Allow building adjacent - the stuck detection will handle any issues
        return false;
    }



    private boolean isValidTowerPosition(BlockPos pos) {
        Level level = this.warden.level();

        // Check if the position is empty
        if (!level.isEmptyBlock(pos)) {
            return false;
        }

        // For tower building, we need solid support below (unless we're building the base)
        BlockPos below = pos.below();
        boolean hasSolidSupport = !level.isEmptyBlock(below) ||
                                 level.getBlockState(below).isSolidRender(level, below) ||
                                 pos.getY() <= level.getMinBuildHeight() + 1;

        if (!hasSolidSupport) {
            return false;
        }

        // Check if there's enough space above for the warden (2 blocks high)
        if (!level.isEmptyBlock(pos.above()) || !level.isEmptyBlock(pos.above(2))) {
            return false;
        }

        // Don't build too far from the warden
        double distance = this.warden.blockPosition().distSqr(pos);
        if (distance > 16.0) { // 4 block radius
            return false;
        }

        return true;
    }

    private boolean isValidBuildPosition(BlockPos pos) {
        Level level = this.warden.level();

        // Check if the position is empty
        if (!level.isEmptyBlock(pos)) {
            return false;
        }

        // Check if there's a solid block below (or we're building on ground level)
        BlockPos below = pos.below();
        if (!level.getBlockState(below).isSolidRender(level, below) && pos.getY() > level.getMinBuildHeight()) {
            return false;
        }

        // Check if there's enough space above for the warden
        if (!level.isEmptyBlock(pos.above()) || !level.isEmptyBlock(pos.above(2))) {
            return false;
        }

        // Make sure we're not building too high
        if (pos.getY() > this.warden.getY() + 5) {
            return false;
        }

        // Make sure we're not building too far from the warden
        double distance = this.warden.blockPosition().distSqr(pos);
        if (distance > 9.0) { // 3 block radius
            return false;
        }

        // Check if this position would actually help reach the target
        double currentDistanceToTarget = this.warden.distanceToSqr(this.target);
        double newDistanceToTarget = pos.distSqr(this.target.blockPosition());

        // Only build if it gets us closer or helps with height
        return newDistanceToTarget <= currentDistanceToTarget + 4.0; // Allow some tolerance
    }

    // NEW FLYING BUILDING METHODS
    private void executeUpwardBuilding() {
        // Stop horizontal movement
        this.warden.getNavigation().stop();

        // Look at target
        this.warden.getLookControl().setLookAt(this.target, 30.0F, 30.0F);

        // FLY UP while building
        this.warden.setNoGravity(true);
        this.warden.setDeltaMovement(0, 0.3, 0); // Gentle upward movement

        // Try to build under the warden while flying
        BlockPos underWarden = this.warden.blockPosition().below();

        if (this.warden.level().isEmptyBlock(underWarden) && this.warden.tryBuildBlock(underWarden)) {
            this.buildAttemptCooldown = 1;
            System.out.println("AI Warden: Built block while flying up! Height: " + this.warden.getY());

            // Check if we've reached target level
            if (Math.abs(this.target.getY() - this.warden.getY()) <= 2.0) {
                this.warden.setNoGravity(false);

                // Check distance to decide next action
                double distance = this.warden.distanceTo(this.target);
                if (distance <= 12.0) {
                    // Close enough - start aggressive chase
                    System.out.println("AI Warden: Reached target level and close! Starting AGGRESSIVE CHASE!");
                    this.warden.getNavigation().moveTo(this.target, 1.5); // Fast movement toward target
                } else {
                    System.out.println("AI Warden: Reached target level! Switching to forward building.");
                }
            }
        } else {
            this.buildAttemptCooldown = 3;
            this.warden.setNoGravity(false); // Land if can't build
            System.out.println("AI Warden: Failed to build while flying, landing...");
        }
    }

    private void executeForwardBuilding() {
        // Stop movement and disable gravity
        this.warden.getNavigation().stop();
        this.warden.setNoGravity(false);

        // Look at target
        this.warden.getLookControl().setLookAt(this.target, 30.0F, 30.0F);

        // Calculate direction to target
        BlockPos wardenPos = this.warden.blockPosition();
        double deltaX = this.target.getX() - this.warden.getX();
        double deltaZ = this.target.getZ() - this.warden.getZ();
        double distance = Math.sqrt(deltaX * deltaX + deltaZ * deltaZ);

        if (distance > 0) {
            // Normalize direction
            int dirX = (int) Math.signum(deltaX);
            int dirZ = (int) Math.signum(deltaZ);

            // Try to build forward toward target
            BlockPos forwardPos = wardenPos.offset(dirX, 0, dirZ);

            if (this.warden.level().isEmptyBlock(forwardPos) && this.warden.tryBuildBlock(forwardPos)) {
                this.buildAttemptCooldown = 1;
                System.out.println("AI Warden: Built forward bridge block toward target!");

                // Move onto the built block
                this.warden.getNavigation().moveTo(forwardPos.getX() + 0.5, forwardPos.getY() + 1, forwardPos.getZ() + 0.5, 1.0);

                // Check if close enough to jump attack
                if (distance <= 8.0) {
                    System.out.println("AI Warden: Close enough to target! Preparing jump attack!");
                    // Give a small jump toward target
                    this.warden.setDeltaMovement(deltaX / distance * 0.5, 0.4, deltaZ / distance * 0.5);
                }
            } else {
                this.buildAttemptCooldown = 3;
                System.out.println("AI Warden: Failed to build forward, retrying...");
            }
        }
    }
}
