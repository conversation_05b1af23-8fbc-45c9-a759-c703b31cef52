package com.aiwardenmod.entity.ai;

import com.aiwardenmod.entity.custom.AIWardenEntity;
import net.minecraft.core.BlockPos;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.ai.goal.Goal;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.pathfinder.Path;
import net.minecraft.world.phys.Vec3;

import java.util.EnumSet;

public class BuildBlocksToTargetGoal extends Goal {
    private final AIWardenEntity warden;
    private final double speedModifier;
    private LivingEntity target;
    private int buildAttemptCooldown = 0;
    private static final int BUILD_ATTEMPT_INTERVAL = 3; // 0.15 seconds - extremely fast building

    public BuildBlocksToTargetGoal(AIWardenEntity warden, double speedModifier) {
        this.warden = warden;
        this.speedModifier = speedModifier;
        // IMPORTANT: Control both MOVE and LOOK to keep warden stationary while building
        this.setFlags(EnumSet.of(Goal.Flag.MOVE, Goal.Flag.LOOK));
    }

    @Override
    public boolean canUse() {
        this.target = this.warden.getTarget();
        if (this.target == null || !this.target.isAlive()) {
            return false;
        }

        // VERY aggressive building conditions - build almost always when target is higher
        double heightDifference = this.target.getY() - this.warden.getY();
        double distance = this.warden.distanceTo(this.target);

        // Build for ANY height difference over 0.5 blocks, regardless of distance
        boolean shouldBuild = heightDifference > 0.5 &&
                             distance < 30.0 &&
                             this.warden.canBuildBlocks();

        if (shouldBuild) {
            System.out.println("AI Warden: AGGRESSIVE Building goal activated! Height diff: " + String.format("%.1f", heightDifference) + ", Distance: " + String.format("%.1f", distance));
        } else {
            System.out.println("AI Warden: Building NOT activated - Height diff: " + String.format("%.1f", heightDifference) + ", Distance: " + String.format("%.1f", distance) + ", Can build: " + this.warden.canBuildBlocks());
        }

        return shouldBuild;
    }

    @Override
    public boolean canContinueToUse() {
        if (this.target == null || !this.target.isAlive()) {
            return false;
        }

        double heightDifference = this.target.getY() - this.warden.getY();
        double distance = this.warden.distanceTo(this.target);

        return heightDifference >= 1.5 && distance < 20.0 && distance > 2.0;
    }

    @Override
    public void start() {
        this.buildAttemptCooldown = 0;
    }

    @Override
    public void stop() {
        this.target = null;
        this.buildAttemptCooldown = 0;
    }

    @Override
    public void tick() {
        if (this.target == null) {
            return;
        }

        if (this.buildAttemptCooldown > 0) {
            this.buildAttemptCooldown--;
            return;
        }

        // Check if we've reached the same level as target
        double heightDifference = this.target.getY() - this.warden.getY();

        if (heightDifference <= 1.0) {
            // We're at the same level! Switch to attack mode
            System.out.println("AI Warden: Reached target level! Switching to attack mode!");
            this.warden.setAttackMode(true);

            // Jump toward target for aggressive attack
            double deltaX = this.target.getX() - this.warden.getX();
            double deltaZ = this.target.getZ() - this.warden.getZ();
            double distance = Math.sqrt(deltaX * deltaX + deltaZ * deltaZ);

            if (distance > 0 && distance < 8.0) {
                // Powerful jump toward target
                double jumpStrength = 0.6;
                this.warden.setDeltaMovement(
                    (deltaX / distance) * 0.4, // Horizontal velocity toward target
                    jumpStrength, // Upward velocity
                    (deltaZ / distance) * 0.4
                );

                // Play attack sound
                this.warden.playSound(net.minecraft.sounds.SoundEvents.WARDEN_ATTACK_IMPACT, 1.5F, 0.8F);
                System.out.println("AI Warden: Jumping to attack target!");
            }

            // Stop building and let attack goals take over
            return;
        }

        // STAY STATIONARY while building - stop all movement
        this.warden.getNavigation().stop();
        this.warden.setDeltaMovement(0, this.warden.getDeltaMovement().y, 0); // Keep Y velocity for gravity

        // Look at target while building
        this.warden.getLookControl().setLookAt(this.target, 30.0F, 30.0F);

        // Simple building strategy - build directly under warden to lift up
        BlockPos buildPos = findStationaryBuildPosition();

        if (buildPos != null && this.warden.tryBuildBlock(buildPos)) {
            this.buildAttemptCooldown = BUILD_ATTEMPT_INTERVAL;
            System.out.println("AI Warden: Built block at " + buildPos + " (staying stationary)");

            // DON'T move - let the block lift the warden up naturally
            // The warden will automatically be pushed up by the block
        } else {
            this.buildAttemptCooldown = 5; // Short cooldown for retry
            System.out.println("AI Warden: Failed to build, retrying...");
        }
    }

    private BlockPos findSimpleBuildPosition() {
        if (this.target == null) {
            return null;
        }

        BlockPos wardenPos = this.warden.blockPosition();
        BlockPos targetPos = this.target.blockPosition();

        // Calculate direction to target
        Vec3 direction = new Vec3(
            targetPos.getX() - wardenPos.getX(),
            0,
            targetPos.getZ() - wardenPos.getZ()
        ).normalize();

        // Try building 1-2 blocks toward target
        for (int distance = 1; distance <= 2; distance++) {
            BlockPos buildPos = wardenPos.offset(
                (int)(direction.x * distance),
                0,
                (int)(direction.z * distance)
            );

            if (isValidBuildPosition(buildPos)) {
                return buildPos;
            }

            // Also try one block higher
            BlockPos buildPosUp = buildPos.above();
            if (isValidBuildPosition(buildPosUp)) {
                return buildPosUp;
            }
        }

        // Try building directly under warden to lift up
        BlockPos underWarden = wardenPos.below();
        if (isValidBuildPosition(underWarden)) {
            return underWarden;
        }

        return null;
    }

    private BlockPos findStationaryBuildPosition() {
        if (this.target == null) {
            return null;
        }

        BlockPos wardenPos = this.warden.blockPosition();

        // Priority 1: Build directly under the warden to lift it up
        BlockPos underWarden = wardenPos.below();
        if (isValidBuildPosition(underWarden)) {
            return underWarden;
        }

        // Priority 2: Build right next to warden (for when it's against a wall)
        BlockPos[] adjacentPositions = {
            wardenPos.north(),
            wardenPos.south(),
            wardenPos.east(),
            wardenPos.west()
        };

        for (BlockPos pos : adjacentPositions) {
            if (isValidBuildPosition(pos)) {
                return pos;
            }
        }

        // Priority 3: Build one block up and adjacent (for stair effect)
        for (BlockPos pos : adjacentPositions) {
            BlockPos upPos = pos.above();
            if (isValidBuildPosition(upPos)) {
                return upPos;
            }
        }

        return null;
    }

    private boolean isValidBuildPosition(BlockPos pos) {
        Level level = this.warden.level();

        // Check if the position is empty
        if (!level.isEmptyBlock(pos)) {
            return false;
        }

        // Check if there's a solid block below (or we're building on ground level)
        BlockPos below = pos.below();
        if (!level.getBlockState(below).isSolidRender(level, below) && pos.getY() > level.getMinBuildHeight()) {
            return false;
        }

        // Check if there's enough space above for the warden
        if (!level.isEmptyBlock(pos.above()) || !level.isEmptyBlock(pos.above(2))) {
            return false;
        }

        // Make sure we're not building too high
        if (pos.getY() > this.warden.getY() + 5) {
            return false;
        }

        // Make sure we're not building too far from the warden
        double distance = this.warden.blockPosition().distSqr(pos);
        if (distance > 9.0) { // 3 block radius
            return false;
        }

        // Check if this position would actually help reach the target
        double currentDistanceToTarget = this.warden.distanceToSqr(this.target);
        double newDistanceToTarget = pos.distSqr(this.target.blockPosition());

        // Only build if it gets us closer or helps with height
        return newDistanceToTarget <= currentDistanceToTarget + 4.0; // Allow some tolerance
    }
}
