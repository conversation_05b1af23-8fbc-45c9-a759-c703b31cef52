package com.aiwardenmod.entity.ai;

import com.aiwardenmod.entity.custom.AIWardenEntity;
import net.minecraft.core.BlockPos;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.ai.goal.Goal;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.pathfinder.Path;
import net.minecraft.world.phys.Vec3;

import java.util.EnumSet;

public class BuildBlocksToTargetGoal extends Goal {
    private final AIWardenEntity warden;
    private final double speedModifier;
    private LivingEntity target;
    private int buildAttemptCooldown = 0;
    private static final int BUILD_ATTEMPT_INTERVAL = 10; // 0.5 seconds - very fast building

    public BuildBlocksToTargetGoal(AIWardenEntity warden, double speedModifier) {
        this.warden = warden;
        this.speedModifier = speedModifier;
        this.setFlags(EnumSet.of(Goal.Flag.MOVE));
    }

    @Override
    public boolean canUse() {
        this.target = this.warden.getTarget();
        if (this.target == null || !this.target.isAlive()) {
            return false;
        }

        // Use this goal for height differences where building makes sense
        double heightDifference = this.target.getY() - this.warden.getY();
        double distance = this.warden.distanceTo(this.target);

        // Only build if target is significantly higher
        if (heightDifference < 2.0) {
            return false;
        }

        // Don't build if we're too close (let normal attack handle it)
        if (distance < 4.0) {
            return false;
        }

        // Don't build if we're too far away
        if (distance > 20.0) {
            return false;
        }

        // Only let flying handle extremely high targets (10+ blocks)
        if (heightDifference > 10.0) {
            return false;
        }

        // Check if warden can build blocks
        boolean canBuild = this.warden.canBuildBlocks();

        // Debug: Let's add some logging to see if this goal is being used
        if (canBuild) {
            System.out.println("AI Warden: Building goal activated! Height diff: " + heightDifference + ", Distance: " + distance);
        }

        return canBuild;
    }

    @Override
    public boolean canContinueToUse() {
        if (this.target == null || !this.target.isAlive()) {
            return false;
        }

        double heightDifference = this.target.getY() - this.warden.getY();
        double distance = this.warden.distanceTo(this.target);

        return heightDifference >= 1.5 && distance < 20.0 && distance > 2.0;
    }

    @Override
    public void start() {
        this.buildAttemptCooldown = 0;
    }

    @Override
    public void stop() {
        this.target = null;
        this.buildAttemptCooldown = 0;
    }

    @Override
    public void tick() {
        if (this.target == null) {
            return;
        }

        if (this.buildAttemptCooldown > 0) {
            this.buildAttemptCooldown--;
            return;
        }

        // Try to find a good position to build blocks
        BlockPos buildPos = findBuildPosition();
        if (buildPos != null && this.warden.tryBuildBlock(buildPos)) {
            this.buildAttemptCooldown = BUILD_ATTEMPT_INTERVAL;

            // The warden will automatically move onto the block in tryBuildBlock()
            // No need to set navigation here as it's handled in the method
        } else {
            // If we can't build, try to move closer to target
            this.warden.getNavigation().moveTo(this.target, this.speedModifier);
            this.buildAttemptCooldown = 5; // Very short cooldown for movement attempts
        }
    }

    private BlockPos findBuildPosition() {
        if (this.target == null) {
            return null;
        }

        Level level = this.warden.level();
        BlockPos wardenPos = this.warden.blockPosition();
        BlockPos targetPos = this.target.blockPosition();

        // Calculate direction towards target
        Vec3 direction = new Vec3(targetPos.getX() - wardenPos.getX(), 0, targetPos.getZ() - wardenPos.getZ()).normalize();

        // First, try building directly under the warden to lift it up
        BlockPos underWarden = wardenPos.below();
        if (isValidBuildPosition(underWarden) && underWarden.getY() >= wardenPos.getY() - 1) {
            System.out.println("AI Warden: Building directly under warden at " + underWarden);
            return underWarden;
        }

        // Next, try building right next to the warden in the direction of the target
        for (int distance = 1; distance <= 2; distance++) {
            BlockPos nextToWarden = wardenPos.offset((int)(direction.x * distance), 0, (int)(direction.z * distance));

            // Try at warden's level and one level up
            for (int heightOffset = 0; heightOffset <= 1; heightOffset++) {
                BlockPos buildPos = nextToWarden.above(heightOffset);

                if (isValidBuildPosition(buildPos)) {
                    double targetHeight = this.target.getY();
                    double currentHeight = this.warden.getY();
                    double buildHeight = buildPos.getY() + 1; // +1 because warden will stand on top

                    if (buildHeight > currentHeight && buildHeight <= targetHeight + 2) {
                        System.out.println("AI Warden: Building next to warden at " + buildPos);
                        return buildPos;
                    }
                }
            }
        }

        // Finally, try positions in a small area around the warden
        for (int attempts = 0; attempts < 4; attempts++) {
            double angle = (attempts * Math.PI / 2.0); // 4 directions around the warden
            double offsetX = Math.cos(angle) * 1.0;
            double offsetZ = Math.sin(angle) * 1.0;

            BlockPos candidatePos = wardenPos.offset((int)offsetX, 0, (int)offsetZ);

            // Check multiple heights
            for (int heightOffset = 0; heightOffset <= 2; heightOffset++) {
                BlockPos buildPos = candidatePos.above(heightOffset);

                if (isValidBuildPosition(buildPos)) {
                    double targetHeight = this.target.getY();
                    double currentHeight = this.warden.getY();
                    double buildHeight = buildPos.getY() + 1; // +1 because warden will stand on top

                    if (buildHeight > currentHeight && buildHeight <= targetHeight + 2) {
                        return buildPos;
                    }
                }
            }
        }

        return null;
    }

    private boolean isValidBuildPosition(BlockPos pos) {
        Level level = this.warden.level();

        // Check if the position is empty
        if (!level.isEmptyBlock(pos)) {
            return false;
        }

        // Check if there's a solid block below (or we're building on ground level)
        BlockPos below = pos.below();
        if (!level.getBlockState(below).isSolidRender(level, below) && pos.getY() > level.getMinBuildHeight()) {
            return false;
        }

        // Check if there's enough space above for the warden
        if (!level.isEmptyBlock(pos.above()) || !level.isEmptyBlock(pos.above(2))) {
            return false;
        }

        // Make sure we're not building too high
        if (pos.getY() > this.warden.getY() + 5) {
            return false;
        }

        // Make sure we're not building too far from the warden
        double distance = this.warden.blockPosition().distSqr(pos);
        if (distance > 9.0) { // 3 block radius
            return false;
        }

        // Check if this position would actually help reach the target
        double currentDistanceToTarget = this.warden.distanceToSqr(this.target);
        double newDistanceToTarget = pos.distSqr(this.target.blockPosition());

        // Only build if it gets us closer or helps with height
        return newDistanceToTarget <= currentDistanceToTarget + 4.0; // Allow some tolerance
    }
}
