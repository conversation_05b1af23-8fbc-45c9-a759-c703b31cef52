package com.aiwardenmod.entity.ai;

import com.aiwardenmod.entity.custom.AIWardenEntity;
import net.minecraft.core.BlockPos;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.ai.goal.Goal;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.pathfinder.Path;
import net.minecraft.world.phys.Vec3;

import java.util.EnumSet;

public class BuildBlocksToTargetGoal extends Goal {
    private final AIWardenEntity warden;
    private final double speedModifier;
    private LivingEntity target;
    private int buildAttemptCooldown = 0;
    private static final int BUILD_ATTEMPT_INTERVAL = 40; // 2 seconds

    public BuildBlocksToTargetGoal(AIWardenEntity warden, double speedModifier) {
        this.warden = warden;
        this.speedModifier = speedModifier;
        this.setFlags(EnumSet.of(Goal.Flag.MOVE));
    }

    @Override
    public boolean canUse() {
        this.target = this.warden.getTarget();
        if (this.target == null || !this.target.isAlive()) {
            return false;
        }

        // Only use this goal if the target is significantly higher than the warden
        double heightDifference = this.target.getY() - this.warden.getY();
        if (heightDifference < 2.0) {
            return false;
        }

        // Check if warden can build blocks and is within reasonable range
        double distance = this.warden.distanceTo(this.target);
        return this.warden.canBuildBlocks() && distance < 16.0 && distance > 3.0;
    }

    @Override
    public boolean canContinueToUse() {
        if (this.target == null || !this.target.isAlive()) {
            return false;
        }

        double heightDifference = this.target.getY() - this.warden.getY();
        double distance = this.warden.distanceTo(this.target);
        
        return heightDifference >= 1.5 && distance < 20.0 && distance > 2.0;
    }

    @Override
    public void start() {
        this.buildAttemptCooldown = 0;
    }

    @Override
    public void stop() {
        this.target = null;
        this.buildAttemptCooldown = 0;
    }

    @Override
    public void tick() {
        if (this.target == null) {
            return;
        }

        if (this.buildAttemptCooldown > 0) {
            this.buildAttemptCooldown--;
            return;
        }

        // Try to find a good position to build blocks
        BlockPos buildPos = findBuildPosition();
        if (buildPos != null && this.warden.tryBuildBlock(buildPos)) {
            this.buildAttemptCooldown = BUILD_ATTEMPT_INTERVAL;
            
            // Move towards the built block
            this.warden.getNavigation().moveTo(buildPos.getX(), buildPos.getY() + 1, buildPos.getZ(), this.speedModifier);
        } else {
            // If we can't build, try to move closer to target
            this.warden.getNavigation().moveTo(this.target, this.speedModifier);
            this.buildAttemptCooldown = 20; // Shorter cooldown for movement attempts
        }
    }

    private BlockPos findBuildPosition() {
        if (this.target == null) {
            return null;
        }

        Level level = this.warden.level();
        BlockPos wardenPos = this.warden.blockPosition();
        BlockPos targetPos = this.target.blockPosition();
        
        // Calculate direction towards target
        Vec3 direction = new Vec3(targetPos.getX() - wardenPos.getX(), 0, targetPos.getZ() - wardenPos.getZ()).normalize();
        
        // Try positions in a small area around the warden, prioritizing towards the target
        for (int attempts = 0; attempts < 8; attempts++) {
            double angle = (attempts * Math.PI / 4.0); // 8 directions around the warden
            double offsetX = Math.cos(angle) * 2.0;
            double offsetZ = Math.sin(angle) * 2.0;
            
            // Bias towards target direction
            if (attempts < 3) {
                offsetX += direction.x * 1.5;
                offsetZ += direction.z * 1.5;
            }
            
            BlockPos candidatePos = wardenPos.offset((int)offsetX, 0, (int)offsetZ);
            
            // Check multiple heights
            for (int heightOffset = 0; heightOffset <= 3; heightOffset++) {
                BlockPos buildPos = candidatePos.above(heightOffset);
                
                if (isValidBuildPosition(buildPos)) {
                    // Prefer positions that get us closer to target height
                    double targetHeight = this.target.getY();
                    double currentHeight = this.warden.getY();
                    double buildHeight = buildPos.getY() + 1; // +1 because warden will stand on top
                    
                    if (buildHeight > currentHeight && buildHeight <= targetHeight + 2) {
                        return buildPos;
                    }
                }
            }
        }
        
        return null;
    }

    private boolean isValidBuildPosition(BlockPos pos) {
        Level level = this.warden.level();
        
        // Check if the position is empty
        if (!level.isEmptyBlock(pos)) {
            return false;
        }
        
        // Check if there's a solid block below (or we're building on ground level)
        BlockPos below = pos.below();
        if (!level.getBlockState(below).isSolidRender(level, below) && pos.getY() > level.getMinBuildHeight()) {
            return false;
        }
        
        // Check if there's enough space above for the warden
        if (!level.isEmptyBlock(pos.above()) || !level.isEmptyBlock(pos.above(2))) {
            return false;
        }
        
        // Make sure we're not building too high
        if (pos.getY() > this.warden.getY() + 5) {
            return false;
        }
        
        // Make sure we're not building too far from the warden
        double distance = this.warden.blockPosition().distSqr(pos);
        if (distance > 9.0) { // 3 block radius
            return false;
        }
        
        // Check if this position would actually help reach the target
        double currentDistanceToTarget = this.warden.distanceToSqr(this.target);
        double newDistanceToTarget = pos.distSqr(this.target.blockPosition());
        
        // Only build if it gets us closer or helps with height
        return newDistanceToTarget <= currentDistanceToTarget + 4.0; // Allow some tolerance
    }
}
